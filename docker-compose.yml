services:
  TrendAnalysis:
    image: trendanalysis:latest
    container_name: TrendAnalysis
    ports:
      - "8080:8080"
      - "9999:9999"
    environment:
      - TZ=Asia/Shanghai
      - JAVA_OPTS=-Xms2048m -Xmx4096m
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=8080
      - LONGBRIDGE_MOCK=true
    command:
      - java
      - -jar
      - app.jar
    logging:
      driver: loki
      options:
        loki-url: "http://172.18.0.3:3100/loki/api/v1/push"
        max-size: "100m"
        max-file: "1"
    volumes:
      - /home/<USER>/nginx/cert:/cert
    networks:
      - TrendAnalysis-Network
networks:
  TrendAnalysis-Network:
    external: true  # 声明这是一个外部网络
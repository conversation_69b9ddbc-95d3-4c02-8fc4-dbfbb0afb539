package com.invest.trendanalysis.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
public class SecurityConfig {
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // 禁用 CSRF (可选,根据需要)
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(authorize -> authorize
                        // 对 Swagger UI 相关路径要求认证
                        .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").authenticated()
                        // 其他请求不需要认证
                        .anyRequest().permitAll())
                // 启用 HTTP Basic 认证
                .httpBasic(withDefaults());
        return http.build();
    }
}
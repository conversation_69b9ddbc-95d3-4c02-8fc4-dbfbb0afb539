package com.invest.trendanalysis.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ObjectUtils;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

@Data
@Configuration
@ConfigurationProperties(prefix = "xxl.job")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class XXLJobConfig {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;

    @Value("${xxl.job.accessToken}")
    private String accessToken;

    @Value("${xxl.job.executor.appname}")
    private String appname;

    @Value("${xxl.job.executor.address}")
    private String address;

    @Value("${xxl.job.executor.ip}")
    private String ip;

    @Value("${xxl.job.executor.port}")
    private int port;

    @Value("${xxl.job.executor.logpath}")
    private String logPath;

    @Value("${xxl.job.executor.logretentiondays}")
    private int logRetentionDays;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        logger.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appname);
        if (ObjectUtils.isEmpty(ip)) {
            ip = findFirstNonLoopbackAddress();
        }
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }

    /**
     * 查找第一个非环回地址，用于在Docker等环境中自动发现IP。
     * 复现 spring-cloud-commons InetUtils.findFirstNonLoopbackHostInfo().getIpAddress() 的逻辑
     * @return IP地址，如果找不到则返回 null
     */
    private String findFirstNonLoopbackAddress() {
        try {
            for (Enumeration<NetworkInterface> nics = NetworkInterface.getNetworkInterfaces(); nics.hasMoreElements();) {
                NetworkInterface nic = nics.nextElement();
                if (!nic.isUp() || nic.isLoopback() || nic.isVirtual()) {
                    continue;
                }
                for (Enumeration<InetAddress> addrs = nic.getInetAddresses(); addrs.hasMoreElements();) {
                    InetAddress addr = addrs.nextElement();
                    if (!addr.isLoopbackAddress() && addr.isSiteLocalAddress() && !addr.getHostAddress().contains(":")) {
                        logger.info("自动发现IP地址: {}", addr.getHostAddress());
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            logger.error("无法查找网络接口", e);
        }
        logger.warn("无法找到合适的非环回IP地址，将使用null，可能导致注册失败。");
        return null;
    }
}
package com.invest.trendanalysis.config;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI springShopOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("接口文档")
                        .contact(new Contact())
                        .description("接口文档")
                        .version("1.0")
                )
                .externalDocs(new ExternalDocumentation()
                        .description("接口文档"));
    }
    @Bean
    public GroupedOpenApi Api() {
        return GroupedOpenApi.builder()
                .group("01.基本接口")
                .packagesToScan("com.invest.trendanalysis.controller")
                .pathsToMatch("/Api/**")
                .build();
    }
    @Bean
    public GroupedOpenApi JobApi() {
        return GroupedOpenApi.builder()
                .group("02.定时任务")
                .packagesToScan("com.invest.trendanalysis.controller")
                .pathsToMatch("/JobApi/**")
                .build();
    }
    @Bean
    public GroupedOpenApi CandlestickApi() {
        return GroupedOpenApi.builder()
                .group("03.K线数据")
                .packagesToScan("com.invest.trendanalysis.controller")
                .pathsToMatch("/CandlestickApi/**")
                .build();
    }
}
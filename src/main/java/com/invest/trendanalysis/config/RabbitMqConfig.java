package com.invest.trendanalysis.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.io.Serial;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class RabbitMqConfig {
    RabbitTemplate rabbitTemplate;
    /**
     * 交易交换机
     */
    public static String TradeExchangeName = "TradeExchange";
    /**
     * 交易队列
     */
    public static String TradeQueueName = "TradeQueue";
    /**
     * 延迟交易交换机
     */
    public static String TradeDelayExchangeName = "TradeDelayExchange";
    /**
     * 死信交换机
     */
    public static String DeadExchangeName = "DeadLetterExchange";
    /**
     * 死信路由
     */
    public static String DeadQueueName = "DeadQueue";
    /**
     * 交易路由键
     */
    public static String TradeRoutingKey = "trade";
    /**
     * 延迟交易路由键
     */
    public static String TradeDelayRoutingKey = "trade_delay";
    /**
     * 死信路由键
     */
    public static String DeadRoutingKey = "dlx_routing_key";
    /**
     * 使用JSON序列化机制，生产者进行消息转换
     * @return RabbitTemplate
     */
    @Primary
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        this.rabbitTemplate = rabbitTemplate;
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return rabbitTemplate;
    }

    /**
     * 使用JSON序列化机制，消费者进行消息转换
     * @return MessageConverter
     */
    @Bean
    public MessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }
    /**
     * 创建交易队列
     *
     * @return Queue
     */
    @Bean
    public Queue bootTradeQueue() {
        return QueueBuilder.durable(TradeQueueName)
                .withArguments(new HashMap<>() {
                    @Serial
                    private static final long serialVersionUID = 7594968149692575028L;

                    {
                        put("x-dead-letter-exchange", DeadExchangeName);
                        put("x-dead-letter-routing-key",DeadRoutingKey);
                    }
                }).build();
    }

    /**
     * 创建交易交换机
     * @return Exchange
     */
    @Bean
    public Exchange bootTradeExchange() {
        return ExchangeBuilder.directExchange(TradeExchangeName)
                .build();
    }

    /**
     * 创建延迟交易交换机
     * @return Exchange
     */
    @Bean
    public Exchange bootTradeDelayExchange() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-delayed-type", "direct");
        return ExchangeBuilder.directExchange(TradeDelayExchangeName)
                .delayed()
                .withArguments(arguments)
                .build();
    }

    /**
     * 创建死信队列
     * @return Queue
     */
    @Bean
    public Queue bootDeadQueue() {
        return QueueBuilder
                .durable(DeadQueueName)
                .build();
    }

    /**
     * 创建死信队列交换机
     * @return Exchange
     */
    @Bean
    public Exchange bootDeadExchange() {
        return ExchangeBuilder.directExchange(DeadExchangeName).build();
    }

    /**
     * 绑定交换机
     * @param bootTradeQueue 队列
     * @param bootTradeExchange 交换机
     * @return Binding
     */
    @Bean
    public Binding bootTradeBinding(Queue bootTradeQueue, Exchange bootTradeExchange) {
        return BindingBuilder.bind(bootTradeQueue).to(bootTradeExchange).with(TradeRoutingKey).noargs();
    }

    @Bean
    public Binding bootTradeDelayBinding(Queue bootTradeQueue, Exchange bootTradeDelayExchange) {
        return BindingBuilder.bind(bootTradeQueue).to(bootTradeDelayExchange).with(TradeDelayRoutingKey).noargs();
    }

    /**
     * 绑定死信路由
     * @param bootDeadExchange bootDeadExchange
     * @param bootDeadQueue bootDeadQueue
     * @return Binding
     */
    @Bean
    public Binding bootDeadBinding(Exchange bootDeadExchange, Queue bootDeadQueue) {
        return BindingBuilder.bind(bootDeadQueue).to(bootDeadExchange).with(DeadRoutingKey).noargs();
    }

    @Bean
    public Binding boodTradeDeadBinding(Exchange bootDeadExchange, Queue bootTradeQueue) {
        return BindingBuilder.bind(bootTradeQueue).to(bootDeadExchange).with(DeadRoutingKey).noargs();
    }
}
package com.invest.trendanalysis.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.invest.trendanalysis.service.Service;
import com.invest.trendanalysis.vo.*;
import com.invest.trendanalysis.vo.form.SearchCandlesticksInfoFormVo;
import com.longport.OpenApiException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 业务接口
 */
@Slf4j
@Tag(name = "业务接口")
@RestController
@RequestMapping("Api")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class Controller {

    final Service service;

    @Operation(summary = "切换环境")
    @Parameter(name = "mock", in = ParameterIn.QUERY, description = "true，模拟环境，false，真实环境", required = true)
    @GetMapping("ChangeEnvironment")
    ResponseEntity<String> ChangeEnvironment(@RequestParam("mock") Boolean mock) {
        return ResponseEntity.ok(service.ChangeEnvironment(mock));
    }

    @GetMapping("GetEnvironment")
    @Operation(summary = "获取当前环境", responses = {
            @ApiResponse(responseCode = "200", description = "返回当前环境状态，true，模拟环境，false，真实环境")
    })
    ResponseEntity<Boolean> GetEnvironment() {
        return ResponseEntity.ok(service.GetEnvironment());
    }

    @Operation(summary = "根据时间区间获取标的日蜡烛图信息",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = SearchCandlesticksInfoFormVo.class)
                    )
            ))
    @PostMapping("SearchCandlesticksInfo")
    ResponseEntity<List<CandlestickVo>> SearchCandlesticksInfo(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception {
        return service.SearchCandlesticksInfo(searchCandlesticksInfoFormVo);
    }

    @PostMapping("SerchSymbolList")
    @Operation(summary = "获取标的列表",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = SearchSymbolVo.class)
                    )
            ))
    ResponseEntity<Page<SecurityVo>> SerchSymbolList(SearchSymbolVo searchSymbolVo) throws Exception {
        return ResponseEntity.ok(service.SerchSymbolList(searchSymbolVo));
    }

    @GetMapping("UpdateSymbolList")
    @Operation(summary = "更新标的列表")
    ResponseEntity<String> UpdateSymbolList() throws Exception {
        return ResponseEntity.ok(service.updateSymbolList());
    }


    @PostMapping("AnalyzeCandlesticksInfo")
    @Operation(summary = "根据时间区间获取标的日蜡烛图形态分析",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = SearchCandlesticksInfoFormVo.class)
                    )
            ))
    ResponseEntity<List<CandlestickAndTypesVo>> AnalyzeCandlesticksInfo(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception {
        return service.AnalyzeCandlesticksInfo(searchCandlesticksInfoFormVo);
    }

    @GetMapping(value = "PushQuoteStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "根据标的获取实时行情")
    @Parameter(name = "symbol", in = ParameterIn.QUERY, description = "标的代码", required = true)
    Flux<PushQuoteVo> PushQuoteStream(@RequestParam("symbol") String[] symbol) throws OpenApiException, ExecutionException, InterruptedException {
        return service.PushQuoteStream(symbol);
    }

    @GetMapping("GetTradingSession")
    @Operation(summary = "获取当天交易时间段")
    ResponseEntity<List<GMT8TradingSesionVo>> GetTradingSession() throws Exception {
        return service.GetTradingSession();
    }

    @GetMapping("GetAccountBalance")
    @Parameter(name = "currency", in = ParameterIn.QUERY, description = "币种，默认USD，HKD")
    @Operation(summary = "获取账户信息")
    ResponseEntity<AccountBalanceVo> GetAccountBalance(@RequestParam(value = "currency", required = false) String currency) throws Exception {
        return service.GetAccountBalance(currency);
    }

    @PostMapping("SubmitOrder")
    @Operation(summary = "委托下单",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = SubmitOrderOptionsVo.class)
                    )
            ))
    ResponseEntity<String> SubmitOrder(SubmitOrderOptionsVo submitOrderOptionsVo) throws Exception {
        return service.SubmitOrder(submitOrderOptionsVo);
    }

    @PostMapping("DelaySubmitOrder")
    @Operation(summary = "延迟委托下单",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = DelaySubmitOrderOptionsVo.class)
                    )
            ))
    ResponseEntity<String> DelaySubmitOrder(DelaySubmitOrderOptionsVo delaySubmitOrderOptionsVo) throws JsonProcessingException {
        return ResponseEntity.ok(service.DelaySubmitOrder(delaySubmitOrderOptionsVo));
    }

    @GetMapping("CancelOrder")
    @Parameter(name = "orderId", in = ParameterIn.QUERY, description = "订单ID")
    @Operation(summary = "撤销订单")
    ResponseEntity<String> CancelOrder(String orderId) throws Exception {
        return service.CancelOrder(orderId);
    }

    @GetMapping("GetOrderDetail")
    @Operation(summary = "获取订单详情")
    @Parameter(name = "orderId", in = ParameterIn.QUERY, description = "订单ID")
    ResponseEntity<OrderDetailVo> GetOrderDetail(String orderId) throws Exception {
        return service.GetOrderDetail(orderId);
    }

    @PostMapping("ReplaceOrder")
    @Operation(summary = "修改订单",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = ReplaceOrderOptionsVo.class)
                    )
            ))
    ResponseEntity<String> ReplaceOrder(ReplaceOrderOptionsVo replaceOrderOptionsVo) throws Exception {
        return service.ReplaceOrder(replaceOrderOptionsVo);
    }

    @PostMapping("GetEstimateMaxPurchaseQuantity")
    @Operation(summary = "预估最大交易数量",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = EstimateMaxPurchaseQuantityOptionsVo.class)
                    )
            ))
    ResponseEntity<EstimateMaxPurchaseQuantityResponseVo> getEstimateMaxPurchaseQuantity(EstimateMaxPurchaseQuantityOptionsVo estimateMaxPurchaseQuantityOptionsVo) throws Exception {
        return ResponseEntity.ok(service.getEstimateMaxPurchaseQuantity(estimateMaxPurchaseQuantityOptionsVo));
    }

    @GetMapping("updatePriceHandler")
    @Operation(summary = "测试更新策略")
    ResponseEntity<String> updatePriceHandler() {
        service.updatePriceHandler();
        return ResponseEntity.ok().body("已更新策略");
    }

    @GetMapping("TestLatency")
    @Operation(summary = "测试延迟", responses = {
            @ApiResponse(responseCode = "200", description = "返回毫秒")
    })
    ResponseEntity<String> TestLatency() throws Exception {
        return ResponseEntity.ok(service.TestLatency());
    }

    @GetMapping("GetCalcIndexes")
    @Operation(summary = "获取标的计算指标")
    ResponseEntity<SecurityCalcIndexVo[]> GetCalcIndexes(@RequestParam("symbol") String[] symbols) throws Exception {
        return ResponseEntity.ok(service.GetCalcIndexes(symbols));
    }

}
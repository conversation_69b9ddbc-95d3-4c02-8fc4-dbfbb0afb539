package com.invest.trendanalysis.controller;

import com.invest.trendanalysis.service.SecService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "SEC数据接口")
@RestController
@RequestMapping("SecApi")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SecController {
    private final SecService secService;
}

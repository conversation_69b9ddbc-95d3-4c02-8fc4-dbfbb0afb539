package com.invest.trendanalysis.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.invest.trendanalysis.service.CandlestickService;
import com.invest.trendanalysis.vo.CandlestickVo;
import com.invest.trendanalysis.vo.form.PageableSearchFormVo;
import com.invest.trendanalysis.vo.form.SearchCandlesticksInfoFormVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * K线数据接口
 */
@Slf4j
@Tag(name = "K线接口")
@RestController
@RequestMapping("CandlestickApi")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CandlestickController {
    private final CandlestickService candlestickService;

    @Operation(summary = "查询K线数据")
    @PostMapping("queryCandlestickData")
    ResponseEntity<Page<CandlestickVo>> queryCandlestickData(@RequestBody PageableSearchFormVo<SearchCandlesticksInfoFormVo> searchForm){
        return ResponseEntity.ok(candlestickService.queryCandlestickData(searchForm));
    }
    @PostMapping("importCandlestickData")
    @Operation(summary = "导入K线数据",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = SearchCandlesticksInfoFormVo.class)
                    )
            ))
    ResponseEntity<String> importCandlestickData(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception {
        return candlestickService.importCandlestickData(searchCandlesticksInfoFormVo);
    }
}
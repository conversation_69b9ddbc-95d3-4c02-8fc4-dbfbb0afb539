package com.invest.trendanalysis.controller;

import com.invest.trendanalysis.job.Job;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定时任务相关接口
 */
@Slf4j
@Tag(name = "业务接口")
@RestController
@RequestMapping("JobApi")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class JobController {
    private final Job job;

    @Operation(summary = "停止follow任务")
    @GetMapping("stopFollow")
    ResponseEntity<String> stopFollow() {
        job.stopFollow();
        return ResponseEntity.ok("已停止follow任务");
    }
}

package com.invest.trendanalysis.service.impl;

import com.invest.trendanalysis.handler.PriceHandler;
import com.invest.trendanalysis.service.PriceService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@org.springframework.stereotype.Service("PriceService")
@Getter
public class PriceServiceImpl implements PriceService {
    public volatile PriceHandler priceHandler;

    public void updatePriceHandler(PriceHandler newHandler){
        this.priceHandler = newHandler;
    };
}

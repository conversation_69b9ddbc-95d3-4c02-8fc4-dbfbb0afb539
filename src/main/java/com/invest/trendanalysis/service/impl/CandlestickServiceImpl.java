package com.invest.trendanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.invest.trendanalysis.mapper.CandlestickMapper;
import com.invest.trendanalysis.service.CandlestickService;
import com.invest.trendanalysis.service.Service;
import com.invest.trendanalysis.vo.CandlestickVo;
import com.invest.trendanalysis.vo.form.PageableSearchFormVo;
import com.invest.trendanalysis.vo.form.SearchCandlesticksInfoFormVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

@org.springframework.stereotype.Service("CandlestickService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CandlestickServiceImpl extends ServiceImpl<CandlestickMapper, CandlestickVo> implements CandlestickService {
    private final Service service;

    private CandlestickService self;

    @Autowired
    public void setSelf(@Lazy CandlestickService self) {
        this.self = self;
    }

    @Override
    public Page<CandlestickVo> queryCandlestickData(PageableSearchFormVo<SearchCandlesticksInfoFormVo> searchForm) {
        // 创建分页对象
        Page<CandlestickVo> page = new Page<>();
        page.setCurrent(searchForm.getCurrent());
        page.setSize(searchForm.getSize());

        // 构建查询条件
        QueryWrapper<CandlestickVo> queryWrapper = new QueryWrapper<>();

        // 获取查询条件
        SearchCandlesticksInfoFormVo condition = searchForm.getCondition();
        if (condition != null) {
            // symbol字段等于参数
            if (StringUtils.hasText(condition.getSymbol())) {
                queryWrapper.eq("symbol", condition.getSymbol());
            }

            // 定义东八区时区
            ZoneId gmt8 = ZoneId.of("Asia/Shanghai");

            // time字段介于startDate和endDate之间
            if (StringUtils.hasText(condition.getStartDate())) {
                LocalDate startDate = LocalDate.parse(condition.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
                ZonedDateTime startDateTime = startDate.atStartOfDay(gmt8);
                queryWrapper.ge("time", startDateTime);
            }

            if (StringUtils.hasText(condition.getEndDate())) {
                LocalDate endDate = LocalDate.parse(condition.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
                ZonedDateTime endDateTime = endDate.plusDays(1).atStartOfDay(gmt8);
                queryWrapper.lt("time", endDateTime);
            }
        }

        // 按时间倒序排列
        queryWrapper.orderByDesc("time");

        return getBaseMapper().selectPage(page, queryWrapper);
    }

    @Override
    public ResponseEntity<String> importCandlestickData(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception {
        if (!StringUtils.hasLength(searchCandlesticksInfoFormVo.getSymbol())) {
            return ResponseEntity.status(500).body("股票代码不能为空");
        }
        if (!StringUtils.hasLength(searchCandlesticksInfoFormVo.getStartDate())) {
            return ResponseEntity.status(500).body("开始时间不能为空");
        }
        if (!StringUtils.hasLength(searchCandlesticksInfoFormVo.getEndDate())) {
            return ResponseEntity.status(500).body("结束时间不能为空");
        }
        // 通过代理对象调用事务方法
        self.executeImport(searchCandlesticksInfoFormVo);
        return ResponseEntity.ok("导入成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeImport(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception {
//        先删除后导入
        LocalDate startDate = LocalDate.parse(searchCandlesticksInfoFormVo.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate endDate = LocalDate.parse(searchCandlesticksInfoFormVo.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        remove(new QueryWrapper<CandlestickVo>().eq("symbol", searchCandlesticksInfoFormVo.getSymbol())
                .ge("time", startDate.atStartOfDay(ZoneId.of("Asia/Shanghai")))
                .le("time", endDate.plusDays(1).atStartOfDay(ZoneId.of("Asia/Shanghai"))));
        ArrayList<CandlestickVo> list = new ArrayList<>();
        service.generateCandlestickList(searchCandlesticksInfoFormVo.getSymbol(), startDate, endDate, list);
        if (!list.isEmpty()) {
            saveBatch(list);
        }
    }

}
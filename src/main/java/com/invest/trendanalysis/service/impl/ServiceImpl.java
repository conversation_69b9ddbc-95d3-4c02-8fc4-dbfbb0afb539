package com.invest.trendanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.invest.trendanalysis.config.RabbitMqConfig;
import com.invest.trendanalysis.handler.CompositePriceHandler;
import com.invest.trendanalysis.handler.OneTimeTriggerHandler;
import com.invest.trendanalysis.manager.CandlesticksManger;
import com.invest.trendanalysis.mapper.FollowMapper;
import com.invest.trendanalysis.service.SecurityService;
import com.invest.trendanalysis.service.Service;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.util.ConfigUtil;
import com.invest.trendanalysis.util.TradeSessionUtil;
import com.invest.trendanalysis.vo.*;
import com.invest.trendanalysis.vo.form.SearchCandlesticksInfoFormVo;
import com.invest.trendanalysis.vo.mapper.AccountBalanceMapper;
import com.invest.trendanalysis.vo.mapper.EstimateMaxPurchaseQuantityOptionsMapper;
import com.invest.trendanalysis.vo.mapper.OrderDetailMapper;
import com.invest.trendanalysis.vo.mapper.SubmitOrderOptionsMapper;
import com.longport.Config;
import com.longport.Market;
import com.longport.OpenApiException;
import com.longport.quote.*;
import com.longport.trade.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static com.longport.quote.CalcIndex.*;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@org.springframework.stereotype.Service("Service")
public class ServiceImpl implements Service {
    private final RedissonClient redissonClient;
    private final ConfigUtil configUtil;
    private final CandlesticksManger candlesticksManger;
    private final TradeSessionUtil tradeSessionUtil;
    private final RabbitTemplate rabbitTemplate;
    private final SecurityService securityService;
    private final FollowMapper followMapper;
    private final CompositePriceHandler compositeHandler = new CompositePriceHandler();
    /**
     * 定时执行任务时间提前量
     */
    private final Long deviationTime = 20L;

    @Override
    public ResponseEntity<List<CandlestickVo>> SearchCandlesticksInfo(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception {
        LocalDate startDate = LocalDate.parse(searchCandlesticksInfoFormVo.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate endDate = LocalDate.parse(searchCandlesticksInfoFormVo.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        String symbol = searchCandlesticksInfoFormVo.getSymbol();
        ArrayList<CandlestickVo> list = new ArrayList<>();
        generateCandlestickList(symbol, startDate, endDate, list);
        return ResponseEntity.ok().body(list);
    }

    @Override
    public ResponseEntity<List<CandlestickAndTypesVo>> AnalyzeCandlesticksInfo(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception {
        LocalDate startDate = LocalDate.parse(searchCandlesticksInfoFormVo.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate endDate = LocalDate.parse(searchCandlesticksInfoFormVo.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        String symbol = searchCandlesticksInfoFormVo.getSymbol();
        ArrayList<CandlestickVo> list = new ArrayList<>();
        generateCandlestickList(symbol, startDate, endDate, list);
        List<CandlestickAndTypesVo> result = candlesticksManger.analyzeAll(list);
        return ResponseEntity.ok().body(result);
    }

    @Override
    public Flux<PushQuoteVo> PushQuoteStream(String[] symbol) {
//        用于存储创建的 QuoteContext，以便 onDispose 回调可以访问它。
        AtomicReference<QuoteContext> contextRef = new AtomicReference<>();
        return Flux.create(emitter -> {
            try {
                Config config = configUtil.getConfig();
                emitter.onDispose(() -> {
                    QuoteContext ctx = contextRef.getAndSet(null);
                    if (ctx != null) {
                        try {
                            ctx.unsubscribe(symbol, SubFlags.Quote).join();
                            ctx.close();
                        }catch (Exception e) {
                            log.error(e.getLocalizedMessage());
                        }
                    }
                });
                QuoteContext.create(config).thenAccept(quoteContext -> {
//                    存储quoteContext
                    if (!contextRef.compareAndSet(null, quoteContext)) {
                        try {
                            quoteContext.close();
                        } catch (Exception e) {
                            log.error(e.getLocalizedMessage());
                        }
                        return;
                    }
                    if (emitter.isCancelled()){
                        log.error("Emitter cancelled after context. Cleanup via onDispose");
                        return;
                    }
                    QuoteHandler quoteHandler = (stock, event) -> {
                        if (emitter.isCancelled()){
                            return;
                        }
                        PushQuoteVo quoteVo = new PushQuoteVo();
                        BeanUtils.copyProperties(event, quoteVo);
//                        价格处理逻辑
                        try {
                            compositeHandler.handlePrice(stock, quoteVo);
                        } catch (Exception e) {
                            emitter.error(e);
                        }
                        emitter.next(quoteVo);
                    };
                    quoteContext.setOnQuote(quoteHandler);
                    try {
//                       SubFlags，Quote：价格，Depth：买卖盘口，Brokers：经纪队列，Trade：逐笔明细
//                        isFirstPush，订阅后是否立刻进行一次数据推送，trade不支持
                        quoteContext.subscribe(symbol, SubFlags.Quote, true)
                                .whenComplete((result, throwable) -> {
                                    if (throwable != null) {
                                        emitter.error(throwable);
                                    }
                                });
                    } catch (OpenApiException e) {
                        emitter.error(e);
                    }
                }).exceptionally(e -> {
                    emitter.error(e);
                    return null;
                });
            } catch (Exception e) {
                emitter.error(e);
            }
        });
    }

    @Override
    public Disposable followPushQuoteStream(String[] symbol) {
        return this.PushQuoteStream(symbol)
                .doOnSubscribe(sub -> log.info("启动行情订阅: {}", Arrays.toString(symbol)))
                .doOnError(e -> log.error("行情流异常", e))
                .doOnCancel(() -> log.warn("行情订阅被取消"))
                .subscribe();
    }

    @Override
    public ResponseEntity<List<GMT8TradingSesionVo>> GetTradingSession() throws Exception {
        return ResponseEntity.ok(this.GetTodayTradingSession());
    }

    public void generateCandlestickList(String symbol, LocalDate startDate, LocalDate endDate, ArrayList<CandlestickVo> list) throws Exception {
        Config config = configUtil.getConfig();
        QuoteContext quoteContext = QuoteContext.create(config).get();
        for (Candlestick candlestick : quoteContext.getHistoryCandlesticksByDate(symbol, Period.Day, AdjustType.NoAdjust, startDate, endDate,TradeSessions.Intraday)
                .get()) {
            CandlestickVo candlestickVo = new CandlestickVo();
            BeanUtils.copyProperties(candlestick, candlestickVo);
            candlestickVo.setSymbol(symbol);
//            计算涨跌幅
            if (candlestickVo.getOpen() != null && candlestickVo.getOpen().compareTo(java.math.BigDecimal.ZERO) != 0) {
                java.math.BigDecimal rise = candlestickVo.getClose().subtract(candlestickVo.getOpen())
                        .divide(candlestickVo.getOpen(), 4, java.math.RoundingMode.HALF_UP)
                        .multiply(new java.math.BigDecimal("100"));
                candlestickVo.setRise(String.format("%+.2f%%", rise.doubleValue()));
            }
            CandlestickUtil.determineCandlestickproperties(candlestickVo);
            list.add(candlestickVo);
        }
        quoteContext.close();
    }

    public List<GMT8TradingSesionVo> GetTodayTradingSession() throws Exception {
//        下午1点执行
        List<GMT8TradingSesionVo> result = new ArrayList<>();
//        当天日期
        LocalDate today = LocalDate.now();
        Config config = configUtil.getConfig();
        QuoteContext quoteContext = QuoteContext.create(config).get();
//        判断当天是否有交易时间,参数中的天指当地时间
        MarketTradingDays marketTradingDays = quoteContext.getTradingDays(Market.US, today, today.plusDays(1)).get();
        if (!Arrays.asList(marketTradingDays.getTradingDays()).contains(today) && !Arrays.asList(marketTradingDays.getHalfTradingDays()).contains(today)) {
            return result;
        }
        MarketTradingSession[] marketTradingSessions = quoteContext.getTradingSession().get();
        if (marketTradingSessions.length == 0) {
            return result;
        }
        quoteContext.close();
        List<MarketTradingSession> usTradingSessions = Arrays.stream(marketTradingSessions)
                .filter(session -> session.getMarket().equals(Market.US))
                .toList();
        if (usTradingSessions.isEmpty()) {
            return result;
        }
        MarketTradingSession usSession = usTradingSessions.get(0);
        TradingSessionInfo[] tradeSessions = usSession.getTradeSessions();
        for (TradingSessionInfo tradeSession : tradeSessions) {
            GMT8TradingSesionVo gmt8TradingSesionVo = new GMT8TradingSesionVo();
            gmt8TradingSesionVo.setTradeSession(tradeSession.getTradeSession());
            gmt8TradingSesionVo.setBeginTime(tradeSessionUtil.newYorkTimeToShanghaiTime(today, tradeSession.getBeginTime()).toLocalDateTime());
            gmt8TradingSesionVo.setEndTime(tradeSessionUtil.newYorkTimeToShanghaiTime(today, tradeSession.getEndTime()).toLocalDateTime());
            result.add(gmt8TradingSesionVo);
        }
        return result;
    }

    @Override
    public ResponseEntity<AccountBalanceVo> GetAccountBalance(String currency) throws Exception {
        Config config = configUtil.getConfig();
        TradeContext tradeContext = TradeContext.create(config).get();
        AccountBalance[] accountBalances = tradeContext.getAccountBalance(Optional.ofNullable(currency).orElse("USD")).get();
        tradeContext.close();
        AccountBalanceVo accountBalanceVo = AccountBalanceMapper.INSTANCE.accountBalanceToAccountBalanceVo(accountBalances[0]);
        log.info("当前账户信息：{}", accountBalanceVo);
        return ResponseEntity.ok().body(accountBalanceVo);
    }

    @Override
    public ResponseEntity<String> SubmitOrder(SubmitOrderOptionsVo submitOrderOptionsVo) throws Exception {
        log.info("下单参数:{}", new ObjectMapper().writeValueAsString(submitOrderOptionsVo));
        Config config = configUtil.getConfig();
        TradeContext tradeContext = TradeContext.create(config).get();
        SubmitOrderOptions submitOrderOptions = SubmitOrderOptionsMapper.INSTANCE.submitOrderOptionsVoToSubmitOrderOptions(submitOrderOptionsVo);
        SubmitOrderResponse submitOrderResponse = tradeContext.submitOrder(submitOrderOptions).get();
        log.info("订单ID:{}", submitOrderResponse.getOrderId());
        tradeContext.close();
        String orderId = submitOrderResponse.getOrderId();
        return ResponseEntity.ok().body(orderId);
    }

    @Override
    public ResponseEntity<String> CancelOrder(String orderId) throws Exception {
        log.info("撤销订单ID:{}", orderId);
        Config config = configUtil.getConfig();
        TradeContext tradeContext = TradeContext.create(config).get();
        tradeContext.cancelOrder(orderId).get();
        tradeContext.close();
        return ResponseEntity.ok().body(orderId + "撤单成功");
    }

    @Override
    public ResponseEntity<OrderDetailVo> GetOrderDetail(String orderId) throws Exception {
        log.info("获取订单详情ID:{}", orderId);
        Config config = configUtil.getConfig();
        TradeContext tradeContext = TradeContext.create(config).get();
        OrderDetail orderDetail = tradeContext.getOrderDetail(orderId).get();
        OrderDetailVo orderDetailVo = OrderDetailMapper.INSTANCE.orderDetailToOrderDetailVo(orderDetail);
        tradeContext.close();
        return ResponseEntity.ok().body(orderDetailVo);
    }

    @Override
    public ResponseEntity<String> ReplaceOrder(ReplaceOrderOptionsVo replaceOrderOptionsVo) throws Exception {
        log.info("修改订单参数:{}", new ObjectMapper().writeValueAsString(replaceOrderOptionsVo));
        Config config = configUtil.getConfig();
        TradeContext tradeContext = TradeContext.create(config).get();
        ReplaceOrderOptions replaceOrderOptions = new ReplaceOrderOptions(replaceOrderOptionsVo.getOrderId(), replaceOrderOptionsVo.getQuantity());
        BeanUtils.copyProperties(replaceOrderOptionsVo, replaceOrderOptions);
        tradeContext.replaceOrder(replaceOrderOptions).get();
        tradeContext.close();
        return ResponseEntity.ok().body("提交成功，订单已修改。");
    }

    @Override
    public EstimateMaxPurchaseQuantityResponseVo getEstimateMaxPurchaseQuantity(EstimateMaxPurchaseQuantityOptionsVo estimateMaxPurchaseQuantityOptionsVo) throws Exception {
        log.info("预估最大交易数量参数:{}", new ObjectMapper().writeValueAsString(estimateMaxPurchaseQuantityOptionsVo));
        Config config = configUtil.getConfig();
        TradeContext tradeContext = TradeContext.create(config).get();
        EstimateMaxPurchaseQuantityOptions estimateMaxPurchaseQuantityOptions = EstimateMaxPurchaseQuantityOptionsMapper.INSTANCE.estimateMaxPurchaseQuantityOptionsVoToEstimateMaxPurchaseQuantityOptions(estimateMaxPurchaseQuantityOptionsVo);
        EstimateMaxPurchaseQuantityResponse estimateMaxPurchaseQuantityResponse = tradeContext.getEstimateMaxPurchaseQuantity(estimateMaxPurchaseQuantityOptions).get();
        tradeContext.close();
        log.info("现金可交易数量:{}", estimateMaxPurchaseQuantityResponse.getCashMaxQty());
        log.info("融资可交易数量:{}", estimateMaxPurchaseQuantityResponse.getMarginMaxQty());
        EstimateMaxPurchaseQuantityResponseVo estimateMaxPurchaseQuantityResponseVo = new EstimateMaxPurchaseQuantityResponseVo();
        BeanUtils.copyProperties(estimateMaxPurchaseQuantityResponse, estimateMaxPurchaseQuantityResponseVo);
        return estimateMaxPurchaseQuantityResponseVo;
    }

    @Override
    public String DelaySubmitOrder(DelaySubmitOrderOptionsVo delaySubmitOrderOptionsVo) throws JsonProcessingException {
        LocalDateTime transactionTime = delaySubmitOrderOptionsVo.getTransactionTime();
        rabbitTemplate.convertAndSend(RabbitMqConfig.TradeDelayExchangeName,
                RabbitMqConfig.TradeDelayRoutingKey,
                delaySubmitOrderOptionsVo,
                message -> {
                    MessageProperties messageProperties = message.getMessageProperties();
                    messageProperties.setMessageId(IdWorker.get32UUID());
                    messageProperties.setHeader("x-delay", Duration.between(LocalDateTime.now(), transactionTime).toMillis() - deviationTime);
                    return message;
                });
        log.info("已发送消息，{}", new ObjectMapper().writeValueAsString(delaySubmitOrderOptionsVo));
        return "已发送消息，" + new ObjectMapper().writeValueAsString(delaySubmitOrderOptionsVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateSymbolList() throws Exception {
        securityService.getBaseMapper().delete(null);
        ArrayList<SecurityVo> list = new ArrayList<>();
        Config config = configUtil.getConfig();
        QuoteContext quoteContext = QuoteContext.create(config).get();
        for (Security security : quoteContext.getSecurityList(Market.US, SecurityListCategory.Overnight).get()) {
            SecurityVo securityVo = new SecurityVo();
            BeanUtils.copyProperties(security, securityVo);
            list.add(securityVo);
        }
        quoteContext.close();
        securityService.saveBatch(list);
        return "更新成功";
    }

    @Override
    public Page<SecurityVo> SerchSymbolList(SearchSymbolVo searchSymbolVo) {
        Page<SecurityVo> page = new Page<>();
        page.setCurrent(searchSymbolVo.getCurrent());
        page.setSize(searchSymbolVo.getSize());
        return securityService.getBaseMapper().selectPage(page, new QueryWrapper<SecurityVo>().like("symbol", searchSymbolVo.getSymbol()));
    }

    /**
     * 关闭连接
     *
     * @param tradeContext tradeContext
     * @param quoteContext quoteContext
     * @throws Exception Exception
     */
    @Override
    public void ContextClose(TradeContext tradeContext, QuoteContext quoteContext) throws Exception {
        if (tradeContext != null) {
            tradeContext.close();
        }
        if (quoteContext != null) {
            quoteContext.close();
        }
    }

    @Override
    public void updatePriceHandler() {
        if (compositeHandler.getHandlers().contains(new OneTimeTriggerHandler())) {
            compositeHandler.removeHandler(new OneTimeTriggerHandler());
        } else {
            compositeHandler.addHandler(new OneTimeTriggerHandler());
        }
    }

    @Override
    public List<String> selectFollowSymbolList() {
        return followMapper.selectFollowSymbolList();
    }

    @Override
    public String TestLatency() throws Exception {
        return configUtil.test();
    }

    @Override
    public SecurityCalcIndexVo[] GetCalcIndexes(String[] symbols) throws Exception {
        Config config = configUtil.getConfig();
        QuoteContext quoteContext = QuoteContext.create(config).get();
        CalcIndex[] indexes = new CalcIndex[]{
                LastDone,
                ChangeValue,
                ChangeRate,
                Volume,
                Turnover,
                YtdChangeRate,
                TurnoverRate,
                TotalMarketValue,
                CapitalFlow,
                Amplitude,
                VolumeRatio,
                PeTtmRatio,
                PbRatio,
                DividendRatioTtm,
                FiveDayChangeRate,
                TenDayChangeRate,
                HalfYearChangeRate,
                FiveMinutesChangeRate,
                ExpiryDate,
                StrikePrice,
                UpperStrikePrice,
                LowerStrikePrice,
                OutstandingQty,
                OutstandingRatio,
                Premium,
                ItmOtm,
                ImpliedVolatility,
                WarrantDelta,
                CallPrice,
                ToCallPrice,
                EffectiveLeverage,
                LeverageRatio,
                ConversionRatio,
                BalancePoint,
                OpenInterest,
                Delta,
                Gamma,
                Theta,
                Vega,
                Rho
        };
        SecurityCalcIndex[] securityCalcIndices = quoteContext.getCalcIndexes(symbols, indexes).get();
        SecurityCalcIndexVo[] securityCalcIndexVos = new SecurityCalcIndexVo[securityCalcIndices.length];
        for (int i = 0; i < securityCalcIndices.length; i++) {
            SecurityCalcIndexVo vo = new SecurityCalcIndexVo();
            BeanUtils.copyProperties(securityCalcIndices[i], vo);
            vo.setSymbol(symbols[i]);
            securityCalcIndexVos[i] = vo;
        }
        quoteContext.close();
        return securityCalcIndexVos;
    }

    @Override
    public String ChangeEnvironment(Boolean mock) {
        RBucket<String> bucket = redissonClient.getBucket(ConfigUtil.mockKey);
        bucket.set(mock.toString());
        return "切换环境成功";
    }

    @Override
    public Boolean GetEnvironment() {
        return configUtil.isMock();
    }
}
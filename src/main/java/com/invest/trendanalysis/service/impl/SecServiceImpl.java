package com.invest.trendanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.invest.trendanalysis.service.*;
import com.invest.trendanalysis.vo.RecentFilingsVo;
import com.invest.trendanalysis.vo.sec.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClient;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.invest.trendanalysis.enumPackage.SecApiEnum.*;
import static com.invest.trendanalysis.enumPackage.SecFormEnum.*;

@Slf4j
@Service("SercService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SecServiceImpl implements SecService {
    private final SecCompanyTickersService secCompanyTickersService;
    private final SecFilingsService secFilingsService;
    private final SecFinancialReportsService secFinancialReportsService;
    private final SecIncomeStatementsVoService secIncomeStatementsVoService;
    private final SecBalanceSheetsVoService secBalanceSheetsVoService;
    private final SecCashFlowStatementsVoService secCashFlowStatementsVoService;
    private final SecEquityAndShareholderReturnVoService secEquityAndShareholderReturnVoService;
    private final ObjectMapper objectMapper;
    @Value("${userAgent}")
    String userAgent;

    /**
     * curl --request GET
     * --url https://www.sec.gov/files/company_tickers.json
     * --header 'User-Agent: ___'
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSecCompanyTickers() {
        RestClient restClient = RestClient.builder()
                .baseUrl(SEC_COMPANY_TICKERS.getUrl())
                .defaultHeader("User-Agent", userAgent)
                .build();

        try {
            String jsonString = restClient.get()
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        log.error("调用{}失败,status code: {}, body: {}", SEC_COMPANY_TICKERS.getUrl(), response.getStatusCode(), new String(response.getBody().readAllBytes()));
                        throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                    })
                    .body(String.class);

            Map<String, SecCompanyTickersVo> companyTickersMap = objectMapper.readValue(jsonString, new TypeReference<>() {});
            secCompanyTickersService.getBaseMapper().delete(null);
            secCompanyTickersService.saveBatch(new ArrayList<>(companyTickersMap.values()));
            log.info("成功写入CIK数据");
        } catch (JsonProcessingException e) {
            log.error("无法解析JSON数据", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("调用SEC API时发生未知错误", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSecCompanyFilings(String cik) {
        String formattedCik = String.format("%010d", Integer.parseInt(cik));
        String fullUrl = SEC_SUBMISSIONS.getUrl() + formattedCik + ".json";

        RestClient restClient = RestClient.builder()
                .defaultHeader("User-Agent", userAgent)
                .build();

        try {
            String jsonString = restClient.get()
                    .uri(fullUrl)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        log.error("调用{}失败,status code: {}, body: {}", fullUrl, response.getStatusCode(), new String(response.getBody().readAllBytes()));
                        throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                    })
                    .body(String.class);

            JsonNode rootNode = objectMapper.readTree(jsonString);
            RecentFilingsVo recentFilings = objectMapper.treeToValue(rootNode.get("filings").get("recent"), RecentFilingsVo.class);
            List<SecFilingsVo> filings = IntStream.range(0, recentFilings.getAccessionNumber().size())
                    .mapToObj(i -> {
                        SecFilingsVo vo = new SecFilingsVo();
                        vo.setCik(cik);
                        vo.setAccessionnumber(recentFilings.getAccessionNumber().get(i));
                        vo.setFilingdate(recentFilings.getFilingDate().get(i));
                        vo.setReportdate(recentFilings.getReportDate().get(i));
                        vo.setForm(recentFilings.getForm().get(i));
                        vo.setPrimarydocument(recentFilings.getPrimaryDocument().get(i));
                        vo.setPrimarydocdescription(recentFilings.getPrimaryDocDescription().get(i));
                        vo.setAcceptanceDateTime(recentFilings.getAcceptanceDateTime().get(i));
                        return vo;
                    })
                    .collect(Collectors.toList());

            if (!filings.isEmpty()) {
                secFilingsService.getBaseMapper().delete(new QueryWrapper<SecFilingsVo>().eq("cik", cik));
                secFilingsService.saveBatch(filings);
                log.info("成功保存{}条报告， CIK：{}", filings.size(), cik);
            }

        } catch (JsonProcessingException e) {
            log.error("JSON数据解析错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("调用SEC API时发生未知错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void printSecGappLabel(String cik) {
        String formattedCik = "CIK" + String.format("%010d", Integer.parseInt(cik));
        String fullUrl = SEC_XBRL_COMPANYFACTS.getUrl().replace("CIK", "") + formattedCik + ".json";

        RestClient restClient = RestClient.builder()
                .defaultHeader("User-Agent", userAgent)
                .build();
        try {
            String jsonString = restClient.get()
                    .uri(fullUrl)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        log.error("调用{}失败,status code: {}, body: {}", fullUrl, response.getStatusCode(), new String(response.getBody().readAllBytes()));
                        throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                    })
                    .body(String.class);

            JsonNode rootNode = objectMapper.readTree(jsonString);
            JsonNode usGaapNode = rootNode.path("facts").path("us-gaap");

            if (usGaapNode.isMissingNode() || !usGaapNode.isObject()) {
                log.warn("在CIK {}的返回数据中没有找到 'us-gaap' facts。", cik);
                return;
            }

            System.out.printf("""
                    ==================================================
                    公司财务指标: %s
                    ==================================================
                    """, rootNode.path("entityName").asText());

            int metricCount = 0;
            Iterator<String> fieldNames = usGaapNode.fieldNames();
            while(fieldNames.hasNext()) {
                String metricName = fieldNames.next();
                JsonNode metricDetails = usGaapNode.get(metricName);
                String label = metricDetails.path("label").asText();
                String description = metricDetails.path("description").asText();

                System.out.printf("""
                        指标名称: %s
                          - 标签: %s
                          - 描述: %s
                        --------------------------------------------------
                        """, metricName, label, description);
                metricCount++;
            }

            System.out.printf("""
                    ==================================================
                    总共找到 %d 条指标。
                    ==================================================
                    """, metricCount);

        } catch (JsonProcessingException e) {
            log.error("JSON数据解析错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("调用SEC API时发生未知错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void updateSecGappLabel(String cik) throws JsonProcessingException {
        List<String> formList = new ArrayList<>();
        formList.add(SEC_FORM_10Q.getFormType());
        formList.add(SEC_FORM_10QA.getFormType());
        formList.add(SEC_FORM_10K.getFormType());
        formList.add(SEC_FORM_10KA.getFormType());
        formList.add(SEC_FORM_8K.getFormType());
        formList.add(SEC_FORM_8KA.getFormType());
        List<SecFilingsVo> list = secFilingsService.getBaseMapper().selectList(new QueryWrapper<SecFilingsVo>().eq("cik", cik).in("form", formList));
        ArrayList<SecFinancialReportsVo> secFinancialReportsVos = new ArrayList<>();
        list.forEach(item->{
            SecFinancialReportsVo secFinancialReportsVo = new SecFinancialReportsVo();
            secFinancialReportsVo.setCikStr(cik);
            secFinancialReportsVo.setAccessionNumber(item.getAccessionnumber());
            secFinancialReportsVo.setReportDate(LocalDate.parse(item.getReportdate(), DateTimeFormatter.ISO_LOCAL_DATE));
            secFinancialReportsVo.setFormType(item.getForm());
            secFinancialReportsVo.setFiledDate(LocalDate.parse(item.getFilingdate(),  DateTimeFormatter.ISO_LOCAL_DATE));
            secFinancialReportsVo.setFiscalYear(Integer.valueOf(item.getReportdate().substring(0, 4)));
            secFinancialReportsVos.add(secFinancialReportsVo);
        });
        secFinancialReportsService.remove(new QueryWrapper<SecFinancialReportsVo>().eq("cik_str", cik));
        secFinancialReportsService.saveBatch(secFinancialReportsVos);
        String formattedCik = "CIK" + String.format("%010d", Integer.parseInt(cik));
        String fullUrl = SEC_XBRL_COMPANYFACTS.getUrl().replace("CIK", "") + formattedCik + ".json";
        RestClient restClient = RestClient.builder()
                .baseUrl(fullUrl)
                .defaultHeader("User-Agent", userAgent)
                .build();
        String jsonString = restClient.get()
                .retrieve()
                .onStatus(HttpStatusCode::isError, (request, response) -> {
                    log.error("调用{}失败,status code: {}, body: {}", fullUrl, response.getStatusCode(), new String(response.getBody().readAllBytes()));
                    throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                })
                .body(String.class);
        JsonNode rootNode = objectMapper.readTree(jsonString);
        JsonNode usGaapNode = rootNode.path("facts").path("us-gaap");
        Iterator<String> fieldNames = usGaapNode.fieldNames();
        ArrayList<SecIncomeStatementsVo> secIncomeStatementsVos = new ArrayList<>();
        ArrayList<SecBalanceSheetsVo> secBalanceSheetsVos = new ArrayList<>();
        ArrayList<SecCashFlowStatementsVo> secCashFlowStatementsVos = new ArrayList<>();
        ArrayList<SecEquityAndShareholderReturnVo> secEquityAndShareholderReturnVos = new ArrayList<>();
        secFinancialReportsVos.forEach(secFinancialReportsVo -> {
            String accessionNumber = secFinancialReportsVo.getAccessionNumber();
            while(fieldNames.hasNext()) {
                String metricName = fieldNames.next();
                SecIncomeStatementsVo secIncomeStatementsVo = new SecIncomeStatementsVo();
                if (metricName.equals("Revenues")){
                    usGaapNode.path("units").path("USD").forEach(item->{
                        if (accessionNumber.equals(item.get("accn").asText())){
                            secIncomeStatementsVo.setReportId(secFinancialReportsVo.getId());
                            secIncomeStatementsVo.setRevenues(item.get("val").decimalValue());
                        }
                    });
                }
                if (metricName.equals("CostOfRevenue")){
                    usGaapNode.path("units").path("USD").forEach(item->{
                        if (accessionNumber.equals(item.get("accn").asText())){
                            secIncomeStatementsVo.setReportId(secFinancialReportsVo.getId());
                            secIncomeStatementsVo.setCostOfRevenue(item.get("val").decimalValue());
                        }
                    });
                }
            }
        });
    }
    private void setSecIncomeStatementsVo(SecIncomeStatementsVo secIncomeStatementsVo,String metricName,String accessionNumber) {

    }
}
package com.invest.trendanalysis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.invest.trendanalysis.vo.*;
import com.invest.trendanalysis.vo.form.SearchCandlesticksInfoFormVo;
import com.longport.OpenApiException;
import com.longport.quote.QuoteContext;
import com.longport.trade.TradeContext;
import org.springframework.http.ResponseEntity;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;

public interface Service {
    ResponseEntity<List<CandlestickVo>> SearchCandlesticksInfo(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception;

    ResponseEntity<List<CandlestickAndTypesVo>> AnalyzeCandlesticksInfo(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception;

    Flux<PushQuoteVo> PushQuoteStream(String[] symbol) throws OpenApiException, ExecutionException, InterruptedException;

    Disposable followPushQuoteStream(String[] symbol);

    ResponseEntity<List<GMT8TradingSesionVo>> GetTradingSession() throws Exception;

    List<GMT8TradingSesionVo> GetTodayTradingSession() throws Exception;

    ResponseEntity<AccountBalanceVo> GetAccountBalance(String currency) throws Exception;

    ResponseEntity<String> SubmitOrder(SubmitOrderOptionsVo submitOrderOptions) throws Exception;

    ResponseEntity<String> CancelOrder(String orderId) throws Exception;

    ResponseEntity<OrderDetailVo> GetOrderDetail(String orderId) throws Exception;

    ResponseEntity<String> ReplaceOrder(ReplaceOrderOptionsVo replaceOrderOptionsVo) throws Exception;

    EstimateMaxPurchaseQuantityResponseVo getEstimateMaxPurchaseQuantity(EstimateMaxPurchaseQuantityOptionsVo estimateMaxPurchaseQuantityOptionsVo) throws Exception;

    String DelaySubmitOrder(DelaySubmitOrderOptionsVo delaySubmitOrderOptionsVo) throws JsonProcessingException;

    String updateSymbolList() throws Exception;

    Page<SecurityVo> SerchSymbolList(SearchSymbolVo searchSymbolVo) throws Exception;

   void ContextClose(TradeContext tradeContext, QuoteContext quoteContext) throws Exception;

    void updatePriceHandler();

    List<String> selectFollowSymbolList();

    String TestLatency() throws Exception;

    SecurityCalcIndexVo[] GetCalcIndexes(String[] symbols) throws Exception;


    String ChangeEnvironment(Boolean mock);

    Boolean GetEnvironment();

    void generateCandlestickList(String symbol, LocalDate startDate, LocalDate endDate, ArrayList<CandlestickVo> list) throws Exception;

}
package com.invest.trendanalysis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.invest.trendanalysis.vo.CandlestickVo;
import com.invest.trendanalysis.vo.form.PageableSearchFormVo;
import com.invest.trendanalysis.vo.form.SearchCandlesticksInfoFormVo;
import org.springframework.http.ResponseEntity;

public interface CandlestickService extends IService<CandlestickVo> {
    Page<CandlestickVo> queryCandlestickData(PageableSearchFormVo<SearchCandlesticksInfoFormVo> searchForm);

    ResponseEntity<String> importCandlestickData(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception;

    void executeImport(SearchCandlesticksInfoFormVo searchCandlesticksInfoFormVo) throws Exception;
}

package com.invest.trendanalysis.job;

import com.baomidou.mybatisplus.core.batch.MybatisBatch;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.invest.trendanalysis.mapper.TradingSessionVoMapper;
import com.invest.trendanalysis.mapper.XxlJobInfoMapper;
import com.invest.trendanalysis.service.Service;
import com.invest.trendanalysis.util.CronUtil;
import com.invest.trendanalysis.vo.GMT8TradingSesionVo;
import com.invest.trendanalysis.vo.TradingSessionVo;
import com.invest.trendanalysis.vo.XxlJobInfoVo;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import static org.awaitility.Awaitility.await;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class Job {
    AtomicBoolean keepRunning = new AtomicBoolean(false);
    Disposable currentSubscription;
    private final Service service;
    private final SqlSessionFactory sqlSessionFactory;
    private final TradingSessionVoMapper tradingSessionVoMapper;
    private final XxlJobInfoMapper xxlJobInfoMapper;
    //    定时任务偏差值1分钟
    final Integer deviation = 1;

    /**
     * 测试任务
     */
    @XxlJob(value = "testJob",init = "init",destroy = "destroy")
    public void testJob(){
        log.info("执行了测试任务");
    }

    /**
     * 写入当天交易时段
     *
     * @throws Exception Exception
     */
    @XxlJob(value = "saveTrendSession", init = "saveTrendSessionInit", destroy = "saveTrendSessionDestroy")
    public void saveTrendSession() throws Exception {
        log.info("开始saveTrendSession任务");
        AtomicReference<LocalDateTime> startTime = new AtomicReference<>();
        AtomicReference<LocalDateTime> endTime = new AtomicReference<>();
        tradingSessionVoMapper.delete(new QueryWrapper<TradingSessionVo>().eq("TS_DATE", LocalDate.now()));
        List<GMT8TradingSesionVo> gmt8TradingSesionVos = service.GetTodayTradingSession();
        ArrayList<TradingSessionVo> list = new ArrayList<>();
        gmt8TradingSesionVos.forEach(item -> {
            TradingSessionVo tradingSessionVo = new TradingSessionVo();
            switch (item.getTradeSession()) {
                case Intraday:
                    tradingSessionVo.setTsSessionType(0);
                    break;
                case Pre:
                    startTime.set(item.getBeginTime());
                    tradingSessionVo.setTsSessionType(1);
                    break;
                case Post:
                    tradingSessionVo.setTsSessionType(2);
                    endTime.set(item.getEndTime());
                    break;
                case Overnight:
                    tradingSessionVo.setTsSessionType(3);
                    break;
                default:
                    break;
            }
            tradingSessionVo.setTsDate(LocalDate.now());
            tradingSessionVo.setTsBeginTime(item.getBeginTime());
            tradingSessionVo.setTsEndTime(item.getEndTime());
            list.add(tradingSessionVo);
        });
        MybatisBatch<TradingSessionVo> mybatisBatch = new MybatisBatch<>(sqlSessionFactory, list);
        MybatisBatch.Method<TradingSessionVo> method = new MybatisBatch.Method<>(TradingSessionVoMapper.class);
        mybatisBatch.execute(method.insert());
        log.info("结束saveTrendSession任务，开始更新follow任务");
        if (list.isEmpty()) {
            xxlJobInfoMapper.update(new UpdateWrapper<XxlJobInfoVo>()
                    .eq("executor_handler", "follow")
                    .set("update_time", LocalDateTime.now())
                    .set("trigger_status", 0));
        } else {
            LocalDateTime realStartTime = startTime.get().minusMinutes(deviation);
            long seconds = Duration.between(realStartTime, endTime.get()).plusMinutes(deviation).getSeconds();
            xxlJobInfoMapper.update(new UpdateWrapper<XxlJobInfoVo>()
                    .eq("executor_handler", "follow")
                    .set("trigger_status", 1)
                    .set("update_time", LocalDateTime.now())
                    .set("executor_timeout", seconds)
                    .set("schedule_conf", CronUtil.build(realStartTime).asString())
                    .set("trigger_next_time", realStartTime.toInstant(ZoneOffset.of("+8")).toEpochMilli()));
        }
        log.info("结束更新follow任务");
    }

    /**
     * 更新股票代码任务
     *
     * @throws Exception Exception
     */
    @XxlJob(value = "updateSymbol", init = "updateSymbolInit", destroy = "updateSymbolDestroy")
    public void updateSymbol() throws Exception {
        log.info("开始updateSymbol任务");
        service.updateSymbolList();
        log.info("结束updateSymbol任务");
    }

    /**
     * 跟踪关注股票价格推送
     */
    @XxlJob(value = "follow", init = "followInit", destroy = "followDestroy")
    public void follow() {
        log.info("开始follow任务");
        keepRunning.set(false);
        Thread currentThread = Thread.currentThread();
        currentThread.setName("followJobThread");
        List<String> symbolList = service.selectFollowSymbolList();
        currentSubscription = service.followPushQuoteStream(symbolList.toArray(String[]::new));
        await().forever().until(keepRunning::get);
        log.info("结束follow任务");
    }

    public void stopFollow() {
        keepRunning.set(true);
    }

    public void saveTrendSessionInit() {

    }

    public void saveTrendSessionDestroy() {

    }

    public void updateSymbolInit() {

    }

    public void updateSymbolDestroy() {

    }

    public void followInit() {

    }

    public void followDestroy() {
        currentSubscription.dispose();
    }
    public void init(){

    }
    public void destroy(){

    }
}
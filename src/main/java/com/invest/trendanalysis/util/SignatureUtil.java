package com.invest.trendanalysis.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

public class SignatureUtil {
    // 计算 SHA1 摘要
    public static String sha1Hex(String data) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-1");
        byte[] digest = md.digest(data.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(digest);
    }

    // 计算 HMAC-SHA256 签名,并返回十六进制字符串
    public static String hmacSha256Hex(String key, String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKey);
        byte[] signatureBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(signatureBytes);
    }

    // 将字节数组转换为十六进制字符串
    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 生成请求签名，X-Api-Signature
     *
     * @param method        HTTP 方法, 如 "GET", "POST" 等
     * @param uri           请求 URI
     * @param ts            Unix Timestamp, eg: 1539095200.123
     * @param params        URL 参数字符串
     * @param body          请求体内容 (如果有),若为空字符串则不算入签名
     * @param secret        签名密钥
     * @param authorization accessToken
     * @param appKey        appKey
     * @return X-Api-Signature
     */
    public static String sign(String method, String uri, String ts, String params, String body, String secret, String authorization, String appKey) throws Exception {
        String mtd = method.toUpperCase();
        StringBuilder canonicalRequest = new StringBuilder();
        canonicalRequest.append(mtd)
                .append("|")
                .append(uri)
                .append("|")
                .append(params)
                .append("|")
                .append("authorization:").append(authorization)
                .append("\n")
                .append("x-api-key:").append(appKey)
                .append("\n")
                .append("x-timestamp:").append(ts)
                .append("\n")
                .append("|authorization;x-api-key;x-timestamp|");

        // 如果请求体不为空,则计算 SHA1 摘要后拼接到 canonical_request 尾部
        if (body != null && !body.isEmpty()) {
            String payloadHash = sha1Hex(body);
            canonicalRequest.append(payloadHash);
        }

        // 构造 sign_str: "HMAC-SHA256|" + SHA1(canonical_request)
        String canonicalRequestHash = sha1Hex(canonicalRequest.toString());
        String signStr = "HMAC-SHA256|" + canonicalRequestHash;
        return "HMAC-SHA256 SignedHeaders=authorization;x-api-key;x-timestamp, Signature=" + hmacSha256Hex(secret, signStr);
    }
}
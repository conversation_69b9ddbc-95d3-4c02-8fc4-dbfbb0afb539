package com.invest.trendanalysis.util;

import com.invest.trendanalysis.vo.CandlestickVo;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class CandlestickUtil {
    /**
     * 定义十字星百分比
     */
    public static final BigDecimal dojiPercentage = BigDecimal.valueOf(0.05);
    /**
     * 定义长实体占据总长度百分比
     */
    public static final BigDecimal entityPercentage = BigDecimal.valueOf(0.5);

    /**
     * 定义阴影占据总长度百分比界定光头光脚
     */
    public static final BigDecimal shadowPercentage = BigDecimal.valueOf(0.05);

    public static void determineCandlestickproperties(CandlestickVo candlestickVo) {
//        判断上涨下跌
        int result = candlestickVo.getOpen().compareTo(candlestickVo.getClose());
        switch (result) {
            case 0:
                candlestickVo.setIsRise(2);
                break;
            case -1:
                candlestickVo.setIsRise(1);
                break;
            case 1:
                candlestickVo.setIsRise(0);
                break;
        }
//        判断是否是十字星,排除掉四价十字星
        if (candlestickVo.getOpen().equals(candlestickVo.getClose())
                && candlestickVo.getHigh().equals(candlestickVo.getLow())
                && candlestickVo.getOpen().equals(candlestickVo.getEntityHigh())
        ) {
            candlestickVo.setIsDoji(0);
        } else if (candlestickVo.getOpen().subtract(candlestickVo.getClose()).abs().compareTo(candlestickVo.getHigh().subtract(candlestickVo.getLow()).abs().multiply(dojiPercentage)) < 0) {
            candlestickVo.setIsDoji(1);
        } else {
            candlestickVo.setIsDoji(0);
        }
        candlestickVo.setEntityHigh(candlestickVo.getOpen().max(candlestickVo.getClose()));
        candlestickVo.setEntityLow(candlestickVo.getOpen().min(candlestickVo.getClose()));
        candlestickVo.setMiddle(candlestickVo.getHigh().add(candlestickVo.getLow()).divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP));
        candlestickVo.setRange(candlestickVo.getHigh().subtract(candlestickVo.getLow()));
        candlestickVo.setEntityRange(candlestickVo.getEntityHigh().subtract(candlestickVo.getEntityLow()));
    }

    /**
     * 判断是否长实体
     *
     * @param candlestickVo 蜡烛图
     * @return 结果
     */
    public static Boolean longEntity(CandlestickVo candlestickVo) {
        return (candlestickVo.getOpen().subtract(candlestickVo.getClose()).abs()).divide(candlestickVo.getHigh().subtract(candlestickVo.getLow()).abs(), RoundingMode.HALF_UP).compareTo(entityPercentage) > 0;
    }
}

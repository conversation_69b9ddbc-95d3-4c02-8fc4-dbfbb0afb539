package com.invest.trendanalysis.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthUtil {

    private final RedissonClient redissonClient;
    private final JwtUtil jwtUtil;
    Long oneHourMills = 3600000L;
    @Value("${token.redis_token_key}")
    private String redisTokenKey;
    @Value("${token.expire_minutes}")
    private Long expireMinutes;
    @Value("${token.renewal_minutes}")
    private Long renewalMinutes;

    /**
     * 校验token
     * @param token token
     * @return 校验结果
     */
    public Boolean verifyToken(String token){
//        从redis中获取token
        RBucket<String> bucket = redissonClient.getBucket(redisTokenKey);
        String redisToken = bucket.get();
        if (redisToken==null){
            return false;
        }else {
//            取到token校验
            boolean result = jwtUtil.verifyToken(redisToken);
//            如果校验通过
            if (result){
//                续期，如果小于1小时
                if (bucket.getExpireTime()<oneHourMills){
                    bucket.expire(Duration.ofMinutes(renewalMinutes));
                }
                return true;
            }else {
                return false;
            }
        }
    }
}

package com.invest.trendanalysis.util;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.longport.Config;
import com.longport.ConfigBuilder;
import com.longport.OpenApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ConfigUtil {
//    redis键
    public static String mockKey = "mock";
//    检测Token过期时间，7天
    Long checkRefreshTime = 604800L;
    @Value("${longbridge.Mock}")
    private Boolean mock;
    @Value("${longbridge.AccessToken}")
    private String accessToken;
    @Value("${longbridge.AppSecret}")
    private String appSecret;
    @Value("${longbridge.AppKey}")
    private String appKey;
    @Value("${longbridge.AccessTokenMock}")
    private String accessTokenMock;
    @Value("${longbridge.AppSecretMock}")
    private String appSecretMock;
    @Value("${longbridge.AppKeyMock}")
    private String appKeyMock;
    @Value("${longbridge.HTTP_API}")
    private String httpApi;
    private final RedissonClient redissonClient;
    private final Environment environment;

    public Boolean isMock(){
        RBucket<String> bucket = redissonClient.getBucket(mockKey);
        String result = bucket.get();
        if (result==null){
            bucket.set(mock.toString());
            return mock;
        }else {
            return Boolean.valueOf(result);
        }
    }

    /**
     * @return Config
     * @throws OpenApiException OpenApiException
     */
    public Config getConfig() throws Exception {
        return isMock() ? getConfigWithStrategy("accessTokenMock", "longbridge.AccessTokenMock", appKeyMock, appSecretMock)
                : getConfigWithStrategy("accessToken", "longbridge.AccessToken", appKey, appSecret);
    }

    private Config getConfigWithStrategy(String bucketKey, String envProperty, String key, String secret) throws Exception {
        RBucket<String> bucket = redissonClient.getBucket(bucketKey);
        String existingToken = bucket.get();
        // 如果从redis里获取到的accessToken不为空
        if (existingToken != null) {
            return processExistingToken(bucket, existingToken, key, secret);
        }
        // 如果redis获取到的accessToken为空，尝试用配置文件里的accessToken去刷新
        return processNewToken(envProperty, key, secret);
    }

    private Config processExistingToken(RBucket<String> bucket, String token, String key, String secret) throws Exception {
        long expireTime = bucket.getExpireTime();
        if (expireTime < checkRefreshTime) {
            String newToken = this.refreshAccessToken(isMock() ? null : token, isMock() ? token : null);
            return buildConfig(key, secret, newToken);
        }
        return buildConfig(key, secret, token);
    }

    private Config processNewToken(String envProperty, String key, String secret) throws Exception {
        String token = environment.getProperty(envProperty);
        String newToken = this.refreshAccessToken(isMock() ? null : token, isMock() ? token : null);
        if (newToken == null) {
            throw new Exception("尝试用配置文件里的accessToken去刷新失败");
        }
        return buildConfig(key, secret, newToken);
    }

    private Config buildConfig(String key, String secret, String token) throws OpenApiException {
        return new ConfigBuilder(key, secret, token).build();
    }

    /**
     * 返回{"code":0,"message":"success","data":{"token":"token", "expired_at":"2025-06-01T00:00:00Z", "account_info":{"member_id":"********", "aaid":"********", "account_channel":"lb", "email":"", "admin_id":"0"}}}
     *
     * @throws Exception Exception
     */
    public String refreshAccessToken(String accessToken, String accessTokenMock) throws Exception {
        Instant now = Instant.now();
        long seconds = now.getEpochSecond();
        int millis = now.getNano() / 1_000_000;
        String timestamp = String.format("%d.%03d", seconds, millis);
//        90天后过期
        String expired = LocalDateTime.now().toInstant(ZoneOffset.UTC).plus(Duration.ofDays(90)).toString();
        if (isMock()) {
            accessToken = accessTokenMock;
            appKey = appKeyMock;
            appSecret = appSecretMock;
        }
        String signature = SignatureUtil.sign("get", "/v1/token/refresh", timestamp, "expired_at=" + expired, "", appSecret, accessToken, appKey);
        String finalAccessToken = accessToken;
        Mono<String> res = WebClient.create(httpApi + "/v1/token/refresh")
                .get()
                .uri(uriBuilder -> uriBuilder.
                        queryParam("expired_at", expired)
                        .build())
                .headers(httpHeaders -> {
                    httpHeaders.set("X-Api-Key", appKey);
                    httpHeaders.set("Authorization", finalAccessToken);
                    httpHeaders.set("X-Api-Signature", signature);
                    httpHeaders.set("X-Timestamp", timestamp);
                })
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(error -> Mono.just(error.toString()));
        String block = res.block();
        log.info(res.block());
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(block);
            if (jsonNode.path("code").asInt() == 0) {
                String newAccessToken = jsonNode.path("data").path("token").asText();
                String expiredAt = jsonNode.path("data").path("expired_at").asText();
                Instant expiredInstant = Instant.parse(expiredAt);
                Instant nowInstant = Instant.now();
                Duration duration = Duration.between(nowInstant, expiredInstant);
                //            持久化保存
                RBucket<Object> bucket;
                if (isMock()) {
                    bucket = redissonClient.getBucket("accessTokenMock");
                } else {
                    bucket = redissonClient.getBucket("accessToken");
                }
                bucket.set(newAccessToken, duration);
                return newAccessToken;
            } else {
                return null;
            }
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * 测试接口，返回延迟
     * @return String
     * @throws Exception Exception
     */
    public String test() throws Exception {
        log.info("从redis里获取accessToken");
        RBucket<String> bucket = redissonClient.getBucket(isMock()?"accessTokenMock":"accessToken");
        String accessToken = bucket.get();
        log.info("获取到accessToken:{}", accessToken);
        Instant now = Instant.now();
        long seconds = now.getEpochSecond();
        int millis = now.getNano() / 1_000_000;
        String timestamp = String.format("%d.%03d", seconds, millis);
        log.info("开始签名");
        String signature = SignatureUtil.sign("get", "/v1/test", timestamp, "", "", appSecret, accessToken, appKey);
        log.info("结束签名，开始发送请求");
//        成功返回测试信息{"code":0,"message":"success}
        Instant sendTime = Instant.now();
        AtomicLong latency = new AtomicLong();
        String res = WebClient.create(httpApi + "/v1/test")
                .get()
                .headers(httpHeaders -> {
                    httpHeaders.set("X-Api-Key", appKey);
                    httpHeaders.set("Authorization", accessToken);
                    httpHeaders.set("X-Api-Signature", signature);
                    httpHeaders.set("X-Timestamp", timestamp);
                })
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response->{
                    Instant receiveTime = Instant.now();
                    latency.set(Duration.between(sendTime, receiveTime).toMillis());
                })
                .block();
        log.info("结束发送请求，测试接口返回结果：{}", res);
        return latency+"ms";
    }
}
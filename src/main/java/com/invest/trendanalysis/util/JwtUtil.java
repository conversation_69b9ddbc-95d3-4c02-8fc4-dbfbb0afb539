package com.invest.trendanalysis.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
@Component
public class JwtUtil {
    @Value("${token.secret}")
    private String secret;
    @Value("${token.expire_minutes}")
    private Long expireMinutes;

    /**
     * 生成Token
     * @return token
     */
    public String generateToken(){
        return JWT.create()
                .withSubject("token")
                .withIssuedAt(Instant.now())
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 验证token
     * @param token token
     * @return 验证结果
     */
    public boolean verifyToken(String token){
        try {
            JWT.require(Algorithm.HMAC256(secret))
                    .build()
                    .verify(token);
            return true;
        }catch (Exception e){
            return false;
        }
    }
}

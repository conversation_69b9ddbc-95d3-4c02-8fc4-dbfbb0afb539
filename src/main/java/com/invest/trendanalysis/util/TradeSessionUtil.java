package com.invest.trendanalysis.util;

import org.springframework.stereotype.Component;

import java.time.*;
@Component
public class TradeSessionUtil {
    public ZonedDateTime newYorkTimeToShanghaiTime(LocalDate localDate, LocalTime newTorkTime){
//        构建纽约时间
        LocalDateTime localDateTime = LocalDateTime.of(localDate, newTorkTime);
        ZonedDateTime newYorkLocalDateTime = ZonedDateTime.of(localDateTime, ZoneId.of("America/New_York"));
//        转换为上海时间
        return newYorkLocalDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
    }
}
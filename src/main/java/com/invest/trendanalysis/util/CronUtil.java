package com.invest.trendanalysis.util;

import com.cronutils.builder.CronBuilder;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.field.expression.FieldExpressionFactory;

import java.time.LocalDateTime;

public class CronUtil {
    public static Cron build(LocalDateTime localDateTime) {
        return CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
                .withMonth(FieldExpressionFactory.on(localDateTime.getMonthValue()))
                .withDoM(FieldExpressionFactory.on(localDateTime.getDayOfMonth()))
                .withHour(FieldExpressionFactory.on(localDateTime.getHour()))
                .withMinute(FieldExpressionFactory.on(localDateTime.getMinute()))
                .withSecond(FieldExpressionFactory.on(localDateTime.getSecond()))
                .withDoW(FieldExpressionFactory.questionMark())
                .instance();
    }
}
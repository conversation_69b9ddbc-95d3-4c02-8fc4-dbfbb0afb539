package com.invest.trendanalysis.enumPackage;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * SEC接口地址
 */
@Getter
@RequiredArgsConstructor()
public enum SecApiEnum {
    /**
     * CIK数据
     */
    SEC_COMPANY_TICKERS("GET","https://www.sec.gov/files/company_tickers.json"),
    /**
     * 申报记录
     */
    SEC_SUBMISSIONS("GET","https://data.sec.gov/submissions/CIK"),
    /**
     * 申报数据
     */
    SEC_XBRL_COMPANYFACTS("GET","https://data.sec.gov/api/xbrl/companyfacts/CIK");
    private final String requestType;
    private final String url;
}
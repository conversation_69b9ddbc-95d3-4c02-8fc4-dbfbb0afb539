package com.invest.trendanalysis.enumPackage;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * SEC利润表指标
 */
@Getter
@RequiredArgsConstructor()
public enum SecIncomeStatementsMetricEnum {
    REVENUES("Revenues","总收入/营业收入"),
    COST_OF_REVENUE("CostOfRevenue","营业成本"),
    GROSS_PROFIT("GrossProfit","毛利润"),
    RESEARCH_AND_DEVELOPMENT_EXPENSE("ResearchAndDevelopmentExpense","研发费用"),
    SELLING_GENERAL_AND_ADMINISTRATIVE_EXPENSE("SellingGeneralAndAdministrativeExpense","销售、一般和管理费用"),
    OPERATING_INCOME_LOSS("OperatingIncomeLoss","营业利润"),
    NONOPERATING_INCOME_EXPENSE("NonoperatingIncomeExpense","非营业收入/支出"),
    INTEREST_EXPENSE("InterestExpense","利息支出"),
    INVESTMENT_INCOME_INTEREST("InvestmentIncomeInterest","投资收益/利息"),
    INCOME_BEFORE_TAX("IncomeBeforeTax","税前利润"),
    INCOME_TAX_EXPENSE_BENEFIT("IncomeTaxExpenseBenefit","所得税费用"),
    NET_INCOME_LOSS("NetIncomeLoss","净利润"),
    EARNINGS_PER_SHARE_BASIC("EarningsPerShareBasic","基本每股收益"),
    EARNINGS_PER_SHARE_DILUTED("EarningsPerShareDiluted","稀释后每股收益"),
    OPERATING_EXPENSES("OperatingExpenses","营业总费用"),
    DEPRECIATION_AND_AMORTIZATION("DepreciationAndAmortization","折旧与摊销"),
    SHARE_BASED_COMPENSATION("ShareBasedCompensation","股权激励费用"),
    GAINS_LOSSES_ON_EXTINGUISHMENT_OF_DEBT("GainsLossesOnExtinguishmentOfDebt","债务清偿损益"),
    GAIN_LOSS_ON_INVESTMENTS("GainLossOnInvestments","投资损益"),
    COMPREHENSIVE_INCOME_NET_OF_TAX("ComprehensiveIncomeNetOfTax","综合收益");
    private final String metric;
    private final String metricDescription;
}

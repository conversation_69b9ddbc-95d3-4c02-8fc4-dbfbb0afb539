package com.invest.trendanalysis.enumPackage;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 长桥接口状态码
 */
@Getter
@RequiredArgsConstructor()
public enum LongBridgeStatusCodeEnum {
    SIGNATURE_INVALID(403201, "签名无效"),
    DUPLICATE_REQUEST(403202, "重复请求，同一个请求没有更换 X-Timestamp"),
    APIKEY_ILLEGAL(403203, "App Key 无效"),
    IP_IS_NOT_ALLOWED(403205, "IP 地址无权访问"),
    TOKEN_EXPIRED(401003, "Access Token 已过期，请刷新 Access Token"),
    IP_REQUEST_RATELIMIT(429001, "IP 访问过于频繁，请稍后再试"),
    API_REQUEST_IS_LIMITED(429002, "接口访问过于频繁，请稍后再试"),
    INTERNAL_ERROR(500000, "服务内部错误，请联系客户经理进行处理");
    private final Integer code;
    private final String description;
}

package com.invest.trendanalysis.enumPackage;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType.*;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType.AbandonedBaby;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType.Engulfing;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType.Harami;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType.HaramiCross;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType.TriStar;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType.*;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType.DojiStar;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.FlatType.SpinningTopBlack;
import com.invest.trendanalysis.candlesticksType.CandlesticksTypes.FlatType.SpinningTopWhite;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 蜡烛图分类枚举
 */
@Getter
@RequiredArgsConstructor()
public enum CandlesticksTypeEnum {
    ONEDAY_CANCANDLESTICKSTYPES(1, Arrays.asList(
            new DojiStar(),
            new com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType.DojiStar(),
            new DragonflyDoji(),
            new GravestoneDoji(),
            new Hammer(),
            new HangingMan(),
            new InvertedHammer(),
            new ShootingStar(),
            new MarubozuWhite(),
            new MarubozuBlack(),
            new LongLowerShadow(),
            new LongUpperShadow(),
            new SpinningTopWhite(),
            new SpinningTopBlack()
    )),
    TWODAY_CANCANDLESTICKSTYPES(2, Arrays.asList(
            new DarkCloudCover(),
            new Piercing(),
            new Engulfing(),
            new com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType.Engulfing(),
            new Harami(),
            new com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType.Harami(),
            new HaramiCross(),
            new com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType.HaramiCross(),
            new RisingWindow(),
            new FallingWindow(),
            new Kicker(),
            new Kicking(),
            new OnNeck(),
            new TweezerBottom(),
            new TweezerTop()
    )),
    THREEDAY_CANCANDLESTICKSTYPES(3, Arrays.asList(
            new MorningDojiStar(),
            new EveningDojiStar(),
            new AbandonedBaby(),
            new com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType.AbandonedBaby(),
            new UpsideTasukiGap(),
            new DownsideTasukiGap(),
            new MorningStar(),
            new EveningStar(),
            new ThreeBlackCrows(),
            new ThreeWhiteSoldiers(),
            new TriStar(),
            new com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType.TriStar(),
            new TwoCrows()
    )),
    FIVEDAY_CANCANDLESTICKSTYPES(5, Arrays.asList(
            new RisingThreeMethods(),
            new FallingThreeMethods()
    ));

    /**
     * 蜡烛图天数
     */
    private final int day;
    /**
     * 蜡烛图类型
     */
    private final List<CandlesticksTypeInterface> list;
}
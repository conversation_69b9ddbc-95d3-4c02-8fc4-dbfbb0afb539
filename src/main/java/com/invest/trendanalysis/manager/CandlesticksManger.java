package com.invest.trendanalysis.manager;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.enumPackage.CandlesticksTypeEnum;
import com.invest.trendanalysis.vo.CandlestickAndTypesVo;
import com.invest.trendanalysis.vo.CandlestickVo;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.util.*;
@Component
public class CandlesticksManger {
    /**
     * 固定数量分析
     * @param candlestickVos candlestickVos
     * @return CandlestickAndTypesVo
     */
    public CandlestickAndTypesVo analyze(List<CandlestickVo> candlestickVos) {
        CandlestickAndTypesVo candlestickAndTypesVo = new CandlestickAndTypesVo();
        List<CandlesticksTypeInterface> candlesticksTypeInterfaces;
        HashSet<String> matchedCandlesticksType = new HashSet<>();
        int size = candlestickVos.size();
//        单组K线最长5个开始分析
        switch (size) {
            case 0:
                return null;
            case 1:
                candlesticksTypeInterfaces = CandlesticksTypeEnum.ONEDAY_CANCANDLESTICKSTYPES.getList();
                for (CandlesticksTypeInterface candlesticksTypeInterface : candlesticksTypeInterfaces) {
                    if (candlesticksTypeInterface.analyze(candlestickVos)){
                        matchedCandlesticksType.add(candlesticksTypeInterface.getCandlesticksTypeName());
                    }
                }
                break;
            case 2:
                candlesticksTypeInterfaces = CandlesticksTypeEnum.TWODAY_CANCANDLESTICKSTYPES.getList();
                for (CandlesticksTypeInterface candlesticksTypeInterface : candlesticksTypeInterfaces) {
                    if (candlesticksTypeInterface.analyze(candlestickVos)){
                        matchedCandlesticksType.add(candlesticksTypeInterface.getCandlesticksTypeName());
                    }
                }
                break;
            case 3:
                candlesticksTypeInterfaces = CandlesticksTypeEnum.THREEDAY_CANCANDLESTICKSTYPES.getList();
                for (CandlesticksTypeInterface candlesticksTypeInterface : candlesticksTypeInterfaces) {
                    if (candlesticksTypeInterface.analyze(candlestickVos)){
                        matchedCandlesticksType.add(candlesticksTypeInterface.getCandlesticksTypeName());
                    }
                }
                break;
            case 5:
                candlesticksTypeInterfaces = CandlesticksTypeEnum.FIVEDAY_CANCANDLESTICKSTYPES.getList();
                for (CandlesticksTypeInterface candlesticksTypeInterface : candlesticksTypeInterfaces) {
                    if (candlesticksTypeInterface.analyze(candlestickVos)){
                        matchedCandlesticksType.add(candlesticksTypeInterface.getCandlesticksTypeName());
                    }
                }
                break;
        }
        candlestickAndTypesVo.setCandlestickVo(candlestickVos.get(size - 1));
        candlestickAndTypesVo.setTypes(matchedCandlesticksType);
        return candlestickAndTypesVo;
    }
    public List<CandlestickAndTypesVo> analyzeAll(List<CandlestickVo> candlestickVos) {
        HashMap<CandlestickVo, HashSet<String>> map = new HashMap<>();
        int size = candlestickVos.size();
        // 分析连续 5 天
        for (int i = 0; i <= size - 5; i++) {
            List<CandlestickVo> fiveDayCandles = candlestickVos.subList(i, i + 5);
            CandlestickAndTypesVo fiveDayResult = analyze(fiveDayCandles);
            this.addCandlestickType(map,fiveDayResult);
        }
        // 分析连续 3 天
        for (int i = 0; i <= size - 3; i++) {
            List<CandlestickVo> threeDayCandles = candlestickVos.subList(i, i + 3);
            CandlestickAndTypesVo threeDayResult = analyze(threeDayCandles);
            this.addCandlestickType(map,threeDayResult);
        }
        // 分析连续 2 天
        for (int i = 0; i <= size - 2; i++) {
            List<CandlestickVo> twoDayCandles = candlestickVos.subList(i, i + 2);
            CandlestickAndTypesVo twoDayResult = analyze(twoDayCandles);
            this.addCandlestickType(map,twoDayResult);
        }
        // 分析单根蜡烛
        for (int i = 0; i < size; i++) {
            List<CandlestickVo> oneCandle = Collections.singletonList(candlestickVos.get(i));
            CandlestickAndTypesVo oneCandleResult = analyze(oneCandle);
            this.addCandlestickType(map,oneCandleResult);
        }
        Map<OffsetDateTime, CandlestickAndTypesVo> sortedMap = new TreeMap<>(OffsetDateTime::compareTo);
        for (Map.Entry<CandlestickVo, HashSet<String>> entry : map.entrySet()) {
            CandlestickVo candlestickVo = entry.getKey();
            Set<String> types = entry.getValue();
            CandlestickAndTypesVo candlestickAndTypesVo = new CandlestickAndTypesVo();
            candlestickAndTypesVo.setCandlestickVo(candlestickVo);
            candlestickAndTypesVo.setDate(candlestickVo.getTimestamp().toLocalDate());
            candlestickAndTypesVo.setTypes(new HashSet<>(types));
            sortedMap.put(candlestickVo.getTimestamp(), candlestickAndTypesVo);
        }
        // 将sortedMap的值添加到result列表中
        return new ArrayList<>(sortedMap.values());
    }
    public void addCandlestickType(HashMap<CandlestickVo, HashSet<String>> map,CandlestickAndTypesVo result) {
        if (map.containsKey(result.getCandlestickVo())){
            map.get(result.getCandlestickVo()).addAll(result.getTypes());
        }else {
            map.put(result.getCandlestickVo(),result.getTypes());
        }
    }
}
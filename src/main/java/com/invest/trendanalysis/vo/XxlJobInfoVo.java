package com.invest.trendanalysis.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 定时任务信息
 */
@Data
@Schema(description = "定时任务信息")
@TableName("xxl_job_info")
public class XxlJobInfoVo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 执行器主键ID
     */
    @TableField("job_group")
    @Schema(description = "执行器主键ID")
    private Integer jobGroup;

    /**
     * 任务描述
     */
    @TableField("job_desc")
    @Schema(description = "任务描述")
    private String jobDesc;

    /**
     * 任务添加时间
     */
    @Schema(description = "任务添加时间")
    private LocalDateTime addTime;

    /**
     * 任务更新时间
     */
    @Schema(description = "任务更新时间")
    private LocalDateTime updateTime;

    /**
     * 作者
     */
    @Schema(description = "作者")
    private String author;

    /**
     * 报警邮件
     */
    @Schema(description = "报警邮件")
    private String alarmEmail;

    /**
     * 调度类型
     */
    @Schema(description = "调度类型")
    private String scheduleType;

    /**
     * 调度配置，值含义取决于调度类型
     */
    @Schema(description = "调度配置，值含义取决于调度类型")
    private String scheduleConf;

    /**
     * 调度过期策略
     */
    @Schema(description = "调度过期策略")
    private String misfireStrategy;

    /**
     * 执行器路由策略
     */
    @Schema(description = "执行器路由策略")
    private String executorRouteStrategy;

    /**
     * 执行器任务handler
     */
    @Schema(description = "执行器任务handler")
    private String executorHandler;

    /**
     * 执行器任务参数
     */
    @Schema(description = "执行器任务参数")
    private String executorParam;

    /**
     * 阻塞处理策略
     */
    @Schema(description = "阻塞处理策略")
    private String executorBlockStrategy;

    /**
     * 任务执行超时时间，单位秒
     */
    @Schema(description = "任务执行超时时间，单位秒")
    private Integer executorTimeout;

    /**
     * 失败重试次数
     */
    @Schema(description = "失败重试次数")
    private Integer executorFailRetryCount;

    /**
     * GLUE类型
     */
    @Schema(description = "GLUE类型")
    private String glueType;

    /**
     * GLUE源代码
     */
    @Schema(description = "GLUE源代码")
    private String glueSource;

    /**
     * GLUE备注
     */
    @Schema(description = "GLUE备注")
    private String glueRemark;

    /**
     * GLUE更新时间
     */
    @Schema(description = "GLUE更新时间")
    private LocalDateTime glueUpdatetime;

    /**
     * 子任务ID，多个逗号分隔
     */
    @Schema(description = "子任务ID，多个逗号分隔")
    private String childJobid;

    /**
     * 调度状态：0-停止，1-运行
     */
    @Schema(description = "调度状态：0-停止，1-运行")
    private Byte triggerStatus;

    /**
     * 上次调度时间
     */
    @Schema(description = "上次调度时间")
    private Long triggerLastTime;

    /**
     * 下次调度时间
     */
    @Schema(description = "下次调度时间")
    private Long triggerNextTime;
}
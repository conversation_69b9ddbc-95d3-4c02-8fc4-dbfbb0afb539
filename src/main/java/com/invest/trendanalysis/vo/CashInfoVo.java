package com.invest.trendanalysis.vo;

import com.longport.trade.CashInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 现金详情
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Tag(name = "现金详情")
public class CashInfoVo extends CashInfo {
    /**
     * 可提现金
     */
    @Schema(description = "可提现金")
    private BigDecimal withdrawCash;
    /**
     * 可用现金
     */
    @Schema(description = "可用现金")
    private BigDecimal availableCash;
    /**
     * 冻结现金
     */
    @Schema(description = "冻结现金")
    private BigDecimal frozenCash;
    /**
     * 待结算现金
     */
    @Schema(description = "待结算现金")
    private BigDecimal settlingCash;
    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;
}
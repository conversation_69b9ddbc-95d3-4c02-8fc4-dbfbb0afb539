package com.invest.trendanalysis.vo;

import com.longport.trade.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;

/**
 * 订单详情
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "订单详情")
public class OrderDetailVo extends OrderDetail {
    @Schema(description = "订单ID")
    private String orderId;
    @Schema(description = "订单状态，" +
            "Unknown，未知，" +
            "NotReported，待提交，" +
            "ReplacedNotReported，待提交 (改单成功)，" +
            "ProtectedNotReported，待提交 (保价订单)，" +
            "VarietiesNotReported，待提交 (条件单)，" +
            "Filled，已成交，" +
            "WaitToNew，已提待报，" +
            "New，已委托，" +
            "WaitToReplace，修改待报，" +
            "PendingReplace，待修改，" +
            "Replaced，已修改，" +
            "PartialFilled，部分成交，" +
            "WaitToCancel，撤销待报，" +
            "PendingCancel，待撤回，" +
            "Rejected，已拒绝，" +
            "CanceledStatus，已撤单，" +
            "Expired，已过期，" +
            "PartialWithdrawal，部分撤单")
    private OrderStatus status;
    @Schema(description = "股票名称")
    private String stockName;
    @Schema(description = "下单数量")
    private BigDecimal quantity;
    @Schema(description = "成交数量。当订单未成交时为 0")
    private BigDecimal executedQuantity;
    @Schema(description = "下单价格。当市价条件单未触发时为空字符串")
    private BigDecimal price;
    @Schema(description = "成交价。当订单未成交时为 0")
    private BigDecimal executedPrice;
    @Schema(description = "下单时间")
    private OffsetDateTime submittedAt;
    @Schema(description = "买卖方向，Buy，买入，Sell，卖出")
    private OrderSide side;
    @Schema(description = "股票代码，使用 ticker.region 格式，例如：AAPL.US")
    private String symbol;
    @Schema(description = "订单类型，" +
            "LO，限价单，" +
            "ELO，增强限价单，" +
            "MO，市价单，" +
            "AO，竞价市价单，" +
            "ALO，竞价限价单，" +
            "ODD，碎股单挂单，" +
            "LIT，触价限价单，" +
            "MIT，触价市价单，" +
            "TSLPAMT，跟踪止损限价单 (跟踪金额)，" +
            "TSLPPCT，跟踪止损限价单 (跟踪涨跌幅)，" +
            "SLO，特殊限价单，不支持改单")
    private OrderType orderType;
    @Schema(description = "最近成交价格。当订单未成交时为空字符串")
    private BigDecimal lastDone;
    @Schema(description = "LIT / MIT 订单触发价格。当订单不是 LIT / MIT 订单为空字符串")
    private BigDecimal triggerPrice;
    @Schema(description = "拒绝信息或备注，默认为空字符串。")
    private String msg;
    @Schema(description = "订单标记，" +
            "Normal - 普通订单，" +
            "LongTerm - 长期单，" +
            "Grey - 暗盘单," +
            "MarginCall - 追加保证金，" +
            "Offline - 离线，" +
            "Creditor - 债权人，" +
            "Debtor - 债务人，" +
            "NonExercise - 没有行权，" +
            "AllocatedSub - 分配子项")
    private OrderTag tag;
    @Schema(description = "订单有效期类型，DAY，当日有效，GTC，撤单前有效，GTD，到期前有效")
    private TimeInForceType timeInForce;
    @Schema(description = "长期单过期时间，格式为 YYYY-MM-DD, 例如：`2022-12-05。不是长期单时，默认为空字符串。")
    private LocalDate expireDate;
    @Schema(description = "最近更新时间，格式为时间戳 (秒)，默认为 0。")
    private OffsetDateTime updatedAt;
    @Schema(description = "条件单触发时间，格式为时间戳 (秒)，默认为 0。")
    private OffsetDateTime triggerAt;
    @Schema(description = "TSLPAMT 订单跟踪金额。当订单不是 TSLPAMT 订单时为空字符串。")
    private BigDecimal trailingAmount;
    @Schema(description = "TSLPPCT 订单跟踪涨跌幅。当订单不是 TSLPPCT 订单时为空字符串。")
    private BigDecimal trailingPercent;
    @Schema(description = "TSLPAMT / TSLPPCT 订单指定价差。当订单不是 TSLPAMT / TSLPPCT 订单时为空字符串。")
    private BigDecimal limitOffset;
    @Schema(description = "条件单触发状态，当订单不是条件单或条件单未触发时, 触发状态为 NOT_USED，" +
            "DEACTIVE - 已失效，" +
            "ACTIVE - 已激活，" +
            "RELEASED - 已触发")
    private TriggerStatus triggerStatus;
    @Schema(description = "结算货币")
    private String currency;
    @Schema(description = "是否允许盘前盘后，美股必填，" +
            "RTH_ONLY - 不允许盘前盘后，" +
            "ANY_TIME - 允许盘前盘后，" +
            "OVERNIGHT - 夜盘")
    private OutsideRTH outsideRth;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "免佣状态，默认为 None，" +
            "None - 无，" +
            "Calculated - 免佣额待计算，" +
            "Pending - 待免佣，" +
            "Ready - 已免佣")
    private CommissionFreeStatus freeStatus;

    @Schema(description = "抵扣状态/返现状态，默认为 NONE，" +
            "NONE - 待结算，" +
            "NO_DATA - 已结算无数据，" +
            "PENDING - 已结算待发放，" +
            "DONE - 已结算已发放")
    private DeductionStatus deductionsStatus;

    @Schema(description = "免佣货币，默认为空字符串")
    private String freeCurrency;
    @Schema(description = "免佣金额，默认为空字符串")
    private BigDecimal freeAmount;
    @Schema(description = "抵扣金额，默认为空字符串")
    private BigDecimal deductionsAmount;
    @Schema(description = "抵扣货币，默认为空字符串")
    private String deductionsCurrency;
    @Schema(description = "平台费抵扣状态/返现状态，默认为 NONE，" +
            "NONE - 待结算，" +
            "NO_DATA - 已结算无数据，" +
            "PENDING - 已结算待发放，" +
            "DONE - 已结算已发放")
    private DeductionStatus platformDeductedStatus;
    @Schema(description = "平台费抵扣金额，默认为空字符串")
    private BigDecimal platformDeductedAmount;
    @Schema(description = "平台费抵扣货币，默认为空字符串")
    private String platformDeductedCurrency;
    @Schema(description = "订单历史明细")
    private OrderHistoryDetail[] history;
    @Schema(description = "订单费用")
    private OrderChargeDetail chargeDetail;
}
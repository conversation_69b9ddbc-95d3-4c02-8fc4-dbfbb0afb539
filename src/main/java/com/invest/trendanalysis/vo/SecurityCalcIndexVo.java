package com.invest.trendanalysis.vo;

import com.longport.quote.SecurityCalcIndex;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "计算指标")
public class SecurityCalcIndexVo extends SecurityCalcIndex {
    @Schema(description = "标的代码")
    private String symbol;

    @Schema(description = "最新价")
    private BigDecimal lastDone;
    
    @Schema(description = "涨跌额")
    private BigDecimal changeValue;
    
    @Schema(description = "涨跌幅")
    private BigDecimal changeRate;
    
    @Schema(description = "成交量")
    private long volume;
    
    @Schema(description = "成交额")
    private BigDecimal turnover;
    
    @Schema(description = "年初至今涨幅")
    private BigDecimal ytdChangeRate;
    
    @Schema(description = "换手率")
    private BigDecimal turnoverRate;
    
    @Schema(description = "总市值")
    private BigDecimal totalMarketValue;
    
    @Schema(description = "流入资金")
    private BigDecimal capitalFlow;
    
    @Schema(description = "振幅")
    private BigDecimal amplitude;
    
    @Schema(description = "量比")
    private BigDecimal volumeRatio;
    
    @Schema(description = "市盈率(TTM)")
    private BigDecimal peTtmRatio;
    
    @Schema(description = "市净率")
    private BigDecimal pbRatio;
    
    @Schema(description = "股息率(TTM)")
    private BigDecimal dividendRatioTtm;
    
    @Schema(description = "五日涨幅")
    private BigDecimal fiveDayChangeRate;
    
    @Schema(description = "十日涨幅")
    private BigDecimal tenDayChangeRate;
    
    @Schema(description = "半年涨幅")
    private BigDecimal halfYearChangeRate;
    
    @Schema(description = "五分钟涨幅")
    private BigDecimal fiveMinutesChangeRate;
    
    @Schema(description = "到期日")
    private LocalDate expiryDate;
    
    @Schema(description = "行权价")
    private BigDecimal strikePrice;
    
    @Schema(description = "上限价")
    private BigDecimal upperStrikePrice;
    
    @Schema(description = "下限价")
    private BigDecimal lowerStrikePrice;
    
    @Schema(description = "街货量")
    private long outstandingQty;
    
    @Schema(description = "街货比")
    private BigDecimal outstandingRatio;
    
    @Schema(description = "溢价率")
    private BigDecimal premium;
    
    @Schema(description = "价内/价外")
    private BigDecimal itmOtm;
    
    @Schema(description = "隐含波动率")
    private BigDecimal impliedVolatility;
    
    @Schema(description = "对冲值")
    private BigDecimal warrantDelta;
    
    @Schema(description = "收回价")
    private BigDecimal callPrice;
    
    @Schema(description = "距收回价")
    private BigDecimal toCallPrice;
    
    @Schema(description = "有效杠杆")
    private BigDecimal effectiveLeverage;
    
    @Schema(description = "杠杆比率")
    private BigDecimal leverageRatio;
    
    @Schema(description = "换股比率")
    private BigDecimal conversionRatio;
    
    @Schema(description = "打和点")
    private BigDecimal balancePoint;
    
    @Schema(description = "未平仓数")
    private long openInterest;
    
    @Schema(description = "Delta值")
    private BigDecimal delta;
    
    @Schema(description = "Gamma值")
    private BigDecimal gamma;
    
    @Schema(description = "Theta值")
    private BigDecimal theta;
    
    @Schema(description = "Vega值")
    private BigDecimal vega;
    
    @Schema(description = "Rho值")
    private BigDecimal rho;
}

package com.invest.trendanalysis.vo.sec;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产负债表数据
 */
@Schema(description="资产负债表数据")
@Data
@TableName(value = "trend.sec_balance_sheets")
public class SecBalanceSheetsVo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    private Integer id;

    /**
     * 关联financial_reports表ID
     */
    @TableField(value = "report_id")
    @Schema(description="关联financial_reports表ID")
    private Long reportId;

    /**
     * 总资产
     */
    @TableField(value = "assets")
    @Schema(description="总资产")
    private BigDecimal assets;

    /**
     * 流动资产
     */
    @TableField(value = "assets_current")
    @Schema(description="流动资产")
    private BigDecimal assetsCurrent;

    /**
     * 现金及现金等价物
     */
    @TableField(value = "cash_and_cash_equivalents")
    @Schema(description="现金及现金等价物")
    private BigDecimal cashAndCashEquivalents;

    /**
     * 短期有价证券
     */
    @TableField(value = "marketable_securities_current")
    @Schema(description="短期有价证券")
    private BigDecimal marketableSecuritiesCurrent;

    /**
     * 应收账款净额
     */
    @TableField(value = "accounts_receivable_net_current")
    @Schema(description="应收账款净额")
    private BigDecimal accountsReceivableNetCurrent;

    /**
     * 存货净额
     */
    @TableField(value = "inventory_net")
    @Schema(description="存货净额")
    private BigDecimal inventoryNet;

    /**
     * 非流动资产
     */
    @TableField(value = "noncurrent_assets")
    @Schema(description="非流动资产")
    private BigDecimal noncurrentAssets;

    /**
     * 固定资产净额
     */
    @TableField(value = "property_plant_and_equipment_net")
    @Schema(description="固定资产净额")
    private BigDecimal propertyPlantAndEquipmentNet;

    /**
     * 商誉
     */
    @TableField(value = "goodwill")
    @Schema(description="商誉")
    private BigDecimal goodwill;

    /**
     * 无形资产净额（除商誉）
     */
    @TableField(value = "intangible_assets_net_excluding_goodwill")
    @Schema(description="无形资产净额（除商誉）")
    private BigDecimal intangibleAssetsNetExcludingGoodwill;

    /**
     * 总负债
     */
    @TableField(value = "liabilities")
    @Schema(description="总负债")
    private BigDecimal liabilities;

    /**
     * 流动负债
     */
    @TableField(value = "liabilities_current")
    @Schema(description="流动负债")
    private BigDecimal liabilitiesCurrent;

    /**
     * 应付账款
     */
    @TableField(value = "accounts_payable_current")
    @Schema(description="应付账款")
    private BigDecimal accountsPayableCurrent;

    /**
     * 应计负债
     */
    @TableField(value = "accrued_liabilities_current")
    @Schema(description="应计负债")
    private BigDecimal accruedLiabilitiesCurrent;

    /**
     * 短期债务
     */
    @TableField(value = "debt_current")
    @Schema(description="短期债务")
    private BigDecimal debtCurrent;

    /**
     * 短期递延收入
     */
    @TableField(value = "deferred_revenue_current")
    @Schema(description="短期递延收入")
    private BigDecimal deferredRevenueCurrent;

    /**
     * 非流动负债
     */
    @TableField(value = "liabilities_noncurrent")
    @Schema(description="非流动负债")
    private BigDecimal liabilitiesNoncurrent;

    /**
     * 长期债务
     */
    @TableField(value = "long_term_debt_noncurrent")
    @Schema(description="长期债务")
    private BigDecimal longTermDebtNoncurrent;

    /**
     * 长期经营租赁负债
     */
    @TableField(value = "operating_lease_liability_noncurrent")
    @Schema(description="长期经营租赁负债")
    private BigDecimal operatingLeaseLiabilityNoncurrent;

    /**
     * 股东权益
     */
    @TableField(value = "stockholders_equity")
    @Schema(description="股东权益")
    private BigDecimal stockholdersEquity;

    /**
     * 留存收益
     */
    @TableField(value = "retained_earnings_accumulated_deficit")
    @Schema(description="留存收益")
    private BigDecimal retainedEarningsAccumulatedDeficit;

    /**
     * 额外实收资本
     */
    @TableField(value = "additional_paid_in_capital")
    @Schema(description="额外实收资本")
    private BigDecimal additionalPaidInCapital;

    /**
     * 库存股价值
     */
    @TableField(value = "treasury_stock_value")
    @Schema(description="库存股价值")
    private BigDecimal treasuryStockValue;

    /**
     * 递延所得税资产净额
     */
    @TableField(value = "deferred_tax_assets_net")
    @Schema(description="递延所得税资产净额")
    private BigDecimal deferredTaxAssetsNet;

    /**
     * 递延所得税负债净额
     */
    @TableField(value = "deferred_tax_liabilities_net")
    @Schema(description="递延所得税负债净额")
    private BigDecimal deferredTaxLiabilitiesNet;

    /**
     * 经营租赁使用权资产
     */
    @TableField(value = "operating_lease_right_of_use_asset")
    @Schema(description="经营租赁使用权资产")
    private BigDecimal operatingLeaseRightOfUseAsset;

    /**
     * 负债和股东权益总计
     */
    @TableField(value = "liabilities_and_stockholders_equity")
    @Schema(description="负债和股东权益总计")
    private BigDecimal liabilitiesAndStockholdersEquity;
}
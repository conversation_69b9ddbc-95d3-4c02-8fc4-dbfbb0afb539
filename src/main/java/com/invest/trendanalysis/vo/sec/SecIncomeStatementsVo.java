package com.invest.trendanalysis.vo.sec;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 利润表数据
 */
@Schema(description="利润表数据")
@Data
@TableName(value = "trend.sec_income_statements")
public class SecIncomeStatementsVo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    private Integer id;

    /**
     * 关联financial_reports表ID
     */
    @TableField(value = "report_id")
    @Schema(description="关联financial_reports表ID")
    private Long reportId;

    /**
     * 总收入/营业收入
     */
    @TableField(value = "revenues")
    @Schema(description="总收入/营业收入")
    private BigDecimal revenues;

    /**
     * 营业成本
     */
    @TableField(value = "cost_of_revenue")
    @Schema(description="营业成本")
    private BigDecimal costOfRevenue;

    /**
     * 毛利润
     */
    @TableField(value = "gross_profit")
    @Schema(description="毛利润")
    private BigDecimal grossProfit;

    /**
     * 研发费用
     */
    @TableField(value = "research_and_development_expense")
    @Schema(description="研发费用")
    private BigDecimal researchAndDevelopmentExpense;

    /**
     * 销售、一般和管理费用
     */
    @TableField(value = "selling_general_and_administrative_expense")
    @Schema(description="销售、一般和管理费用")
    private BigDecimal sellingGeneralAndAdministrativeExpense;

    /**
     * 营业利润
     */
    @TableField(value = "operating_income_loss")
    @Schema(description="营业利润")
    private BigDecimal operatingIncomeLoss;

    /**
     * 非营业收入/支出
     */
    @TableField(value = "nonoperating_income_expense")
    @Schema(description="非营业收入/支出")
    private BigDecimal nonoperatingIncomeExpense;

    /**
     * 利息支出
     */
    @TableField(value = "interest_expense")
    @Schema(description="利息支出")
    private BigDecimal interestExpense;

    /**
     * 投资收益/利息
     */
    @TableField(value = "investment_income_interest")
    @Schema(description="投资收益/利息")
    private BigDecimal investmentIncomeInterest;

    /**
     * 税前利润
     */
    @TableField(value = "income_before_tax")
    @Schema(description="税前利润")
    private BigDecimal incomeBeforeTax;

    /**
     * 所得税费用
     */
    @TableField(value = "income_tax_expense_benefit")
    @Schema(description="所得税费用")
    private BigDecimal incomeTaxExpenseBenefit;

    /**
     * 净利润
     */
    @TableField(value = "net_income_loss")
    @Schema(description="净利润")
    private BigDecimal netIncomeLoss;

    /**
     * 基本每股收益
     */
    @TableField(value = "earnings_per_share_basic")
    @Schema(description="基本每股收益")
    private BigDecimal earningsPerShareBasic;

    /**
     * 稀释后每股收益
     */
    @TableField(value = "earnings_per_share_diluted")
    @Schema(description="稀释后每股收益")
    private BigDecimal earningsPerShareDiluted;

    /**
     * 营业总费用
     */
    @TableField(value = "operating_expenses")
    @Schema(description="营业总费用")
    private BigDecimal operatingExpenses;

    /**
     * 折旧与摊销
     */
    @TableField(value = "depreciation_and_amortization")
    @Schema(description="折旧与摊销")
    private BigDecimal depreciationAndAmortization;

    /**
     * 股权激励费用
     */
    @TableField(value = "share_based_compensation")
    @Schema(description="股权激励费用")
    private BigDecimal shareBasedCompensation;

    /**
     * 债务清偿损益
     */
    @TableField(value = "gains_losses_on_extinguishment_of_debt")
    @Schema(description="债务清偿损益")
    private BigDecimal gainsLossesOnExtinguishmentOfDebt;

    /**
     * 投资损益
     */
    @TableField(value = "gain_loss_on_investments")
    @Schema(description="投资损益")
    private BigDecimal gainLossOnInvestments;

    /**
     * 综合收益
     */
    @TableField(value = "comprehensive_income_net_of_tax")
    @Schema(description="综合收益")
    private BigDecimal comprehensiveIncomeNetOfTax;
}
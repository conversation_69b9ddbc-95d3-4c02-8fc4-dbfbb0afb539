package com.invest.trendanalysis.vo.sec;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 股权与股东回报数据
 */
@Schema(description="股权与股东回报数据")
@Data
@TableName(value = "trend.sec_equity_and_shareholder_return")
public class SecEquityAndShareholderReturnVo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    private Integer id;

    /**
     * 关联financial_reports表ID
     */
    @TableField(value = "report_id")
    @Schema(description="关联financial_reports表ID")
    private Long reportId;

    /**
     * 流通在外的普通股股数
     */
    @TableField(value = "common_stock_shares_outstanding")
    @Schema(description="流通在外的普通股股数")
    private Long commonStockSharesOutstanding;

    /**
     * 基本加权平均流通股数
     */
    @TableField(value = "weighted_average_shares_basic")
    @Schema(description="基本加权平均流通股数")
    private Long weightedAverageSharesBasic;

    /**
     * 稀释后加权平均流通股数
     */
    @TableField(value = "weighted_average_shares_diluted")
    @Schema(description="稀释后加权平均流通股数")
    private Long weightedAverageSharesDiluted;

    /**
     * 授权回购总额
     */
    @TableField(value = "repurchase_authorized_amount")
    @Schema(description="授权回购总额")
    private BigDecimal repurchaseAuthorizedAmount;

    /**
     * 剩余可回购额度
     */
    @TableField(value = "repurchase_remaining_amount")
    @Schema(description="剩余可回购额度")
    private BigDecimal repurchaseRemainingAmount;

    /**
     * 授权发行的普通股总数
     */
    @TableField(value = "common_stock_shares_authorized")
    @Schema(description="授权发行的普通股总数")
    private Long commonStockSharesAuthorized;
}
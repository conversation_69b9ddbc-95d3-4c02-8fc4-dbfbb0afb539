package com.invest.trendanalysis.vo.sec;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "SEC公司CIK数据")
@TableName("sec_company_tickers")
public class SecCompanyTickersVo {
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @TableField("cik_str")
    @Schema(description = "CIK")
    @JsonProperty("cik_str")
    private String cikStr;

    @TableField("ticker")
    @Schema(description = "代码")
    @JsonProperty("ticker")
    private String ticker;

    @TableField("title")
    @Schema(description = "描述")
    @JsonProperty("title")
    private String title;
}
package com.invest.trendanalysis.vo.sec;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
@TableName("sec_financial_reports")
@Schema(description = "财务报告基本信息")
public class SecFinancialReportsVo {
    @Schema(description = "主键")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @Schema(description = "公司CIK")
    @TableField("cik_str")
    private String cikStr;

    @Schema(description = "财报截止日期")
    @TableField("report_date")
    private LocalDate reportDate;

    @Schema(description = "财年")
    @TableField("fiscal_year")
    private Integer fiscalYear;

    @Schema(description = "财报周期 (如 Q1, Q2, FY)")
    @TableField("fiscal_period")
    private String fiscalPeriod;

    @Schema(description = "报告类型 (如 10-K, 10-Q)")
    @TableField("form_type")
    private String formType;

    @Schema(description = "报告提交日期")
    @TableField("filed_date")
    private LocalDate filedDate;

    @Schema(description = "SEC访问号")
    @TableField("accessionNumber")
    private String accessionNumber;
}
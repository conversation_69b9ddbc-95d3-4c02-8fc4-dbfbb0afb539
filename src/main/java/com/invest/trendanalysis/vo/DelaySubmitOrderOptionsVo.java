package com.invest.trendanalysis.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 延迟提交订单信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "延迟提交订单信息")
public class DelaySubmitOrderOptionsVo extends SubmitOrderOptionsVo{
    @Serial
    private static final long serialVersionUID = -5967097511506001561L;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @Schema(description = "设置交易时间，默认东八区，yyyy-MM-dd HH:mm:ss",requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime transactionTime;
}
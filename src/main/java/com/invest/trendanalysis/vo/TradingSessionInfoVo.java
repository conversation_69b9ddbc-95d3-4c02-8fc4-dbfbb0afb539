package com.invest.trendanalysis.vo;

import com.longport.quote.TradeSession;
import com.longport.quote.TradingSessionInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalTime;

/**
 * 交易时段
 */
@Getter
@Setter
@Tag(name = "交易时段")
public class TradingSessionInfoVo extends TradingSessionInfo {
    @Schema(description = "交易开始时间，格式：hhmm")
    private LocalTime beginTime;
    @Schema(description = "交易结束时间，格式：hhmm")
    private LocalTime endTime;
    @Schema(description = "交易时段，NORMAL_TRADE，盘中交易，PRE_TRADE，盘前交易，POST_TRADE，盘后交易，OvernightTrade，夜盘交易")
    private TradeSession tradeSession;

    public String toString() {
        return "TradingSessionInfo [beginTime=" + this.beginTime + ", endTime=" + this.endTime + ", tradeSession=" + this.tradeSession + "]";
    }
}
package com.invest.trendanalysis.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.HashSet;

@Data
@Schema(description = "蜡烛图和类型")
public class CandlestickAndTypesVo {
    @Schema(description = "蜡烛图信息")
    private CandlestickVo candlestickVo;
    @Schema(description = "蜡烛图分析")
    private HashSet<String> types;
    @Schema(description = "对应日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;
}
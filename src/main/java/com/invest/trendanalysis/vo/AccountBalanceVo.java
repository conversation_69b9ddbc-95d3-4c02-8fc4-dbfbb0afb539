package com.invest.trendanalysis.vo;

import com.longport.trade.AccountBalance;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 账户资金信息
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@ToString
@Schema(description = "账户资金信息")
public class AccountBalanceVo extends AccountBalance {
    /**
     * 现金总额
     */
    @Schema(description = "现金总额")
    private BigDecimal totalCash;
    /**
     * 最大融资金额
     */
    @Schema(description = "最大融资金额")
    private BigDecimal maxFinanceAmount;
    /**
     * 剩余融资金额
     */
    @Schema(description = "剩余融资金额")
    private BigDecimal remainingFinanceAmount;
    /**
     * 风控等级，0，安全，1，中风险，2，预警，3，危险
     */
    @Schema(description = "风控等级，0，安全，1，中风险，2，预警，3，危险")
    private int riskLevel;
    /**
     * 追缴保证金
     */
    @Schema(description = "追缴保证金")
    private BigDecimal marginCall;
    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;
    /**
     * 现金详情
     */
    @Schema(description = "现金详情")
    private CashInfoVo[] cashInfos;
    /**
     * 净资产
     */
    @Schema(description = "净资产")
    private BigDecimal netAssets;
    /**
     * 初始保证金
     */
    @Schema(description = "初始保证金")
    private BigDecimal initMargin;
    /**
     * 维持保证金
     */
    @Schema(description = "维持保证金")
    private BigDecimal maintenanceMargin;
    /**
     * 购买力
     */
    @Schema(description = "购买力")
    private BigDecimal buyPower;
}
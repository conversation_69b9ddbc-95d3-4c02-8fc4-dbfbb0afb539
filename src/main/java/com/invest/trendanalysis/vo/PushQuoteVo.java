package com.invest.trendanalysis.vo;

import com.longport.quote.PushQuote;
import com.longport.quote.TradeSession;
import com.longport.quote.TradeStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 推送报价
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "推送报价")
@Data
public class PushQuoteVo extends PushQuote {
    @Schema(description = "最新价")
    private BigDecimal lastDone;
    @Schema(description = "开盘价")
    private BigDecimal open;
    @Schema(description = "最高价")
    private BigDecimal high;
    @Schema(description = "最低价")
    private BigDecimal low;
    @Schema(description = "最新成交的时间戳")
    private OffsetDateTime timestamp;
    @Schema(description = "成交量")
    private long volume;
    @Schema(description = "成交额")
    private BigDecimal turnover;
    @Schema(description = "交易状态，NORMAL，正常交易，HALTED，停牌，DELISTED，退市，FUSE，熔断，PREPARE_LIST，新股待上市，CODE_MOVED，代码变更，TO_BE_OPENED，待开盘，SPLIT_STOCK_HALTS，拆合股暂停交易，EXPIRED，已到期 (衍生品)，WARRANT_PREPARE_LIST，轮证待上市，SUSPEND_TRADE，终止交易")
    private TradeStatus tradeStatus;
    @Schema(description = "交易时段，NORMAL_TRADE，盘中交易，PRE_TRADE，盘前交易，POST_TRADE，盘后交易，OvernightTrade，夜盘交易")
    private TradeSession tradeSession;
}

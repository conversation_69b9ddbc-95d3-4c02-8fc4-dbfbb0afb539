package com.invest.trendanalysis.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longport.quote.Candlestick;
import com.longport.quote.TradeSession;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 蜡烛图信息
 */
@Getter
@Setter
@Tag(name = "蜡烛图信息")
@TableName("candlestick")
public class CandlestickVo extends Candlestick {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    /**
     * 股票代码
     */
    @TableField("symbol")
    @Schema(description = "股票代码")
    private String symbol;
    /**
     * 收盘价
     */
    @TableField("close")
    @Schema(description = "收盘价")
    private BigDecimal close;
    /**
     * 开盘价
     */
    @TableField("open")
    @Schema(description = "开盘价")
    private BigDecimal open;
    /**
     * 最低价
     */
    @TableField("low")
    @Schema(description = "最低价")
    private BigDecimal low;
    /**
     * 最高价
     */
    @TableField("high")
    @Schema(description = "最高价")
    private BigDecimal high;
    /**
     * 成交量
     */
    @TableField("volume")
    @Schema(description = "成交量")
    private long volume;
    /**
     * 成交额
     */
    @TableField("turnover")
    @Schema(description = "成交额")
    private BigDecimal turnover;
    /**
     * 时间戳
     */
    @TableField("time")
    @Schema(description = "时间戳")
    private OffsetDateTime timestamp;

    @TableField(value = "trade_session")
    @Schema(description = "交易时段，0，盘中，1，盘前，2，盘后，3，夜盘")
    private TradeSession tradeSession;
    /**
     * 是否上涨，0，下跌，1，上涨，2，不变
     */
    @TableField(exist = false)
    @Schema(description = "是否上涨，0，下跌，1，上涨，2，不变")
    private Integer isRise;
    /**
     * 是否是十字星，0，否，1，是
     */
    @TableField(exist = false)
    @Schema(description = "是否是十字星，0，否，1，是")
    private Integer isDoji;
    /**
     * 实体最高价
     */
    @TableField(exist = false)
    @Schema(description = "实体最高价")
    private BigDecimal entityHigh = BigDecimal.ZERO;
    /**
     * 实体最低价
     */
    @TableField(exist = false)
    @Schema(description = "实体最低价")
    private BigDecimal entityLow = BigDecimal.ZERO;

    /**
     * 中间价，(开盘价+收盘价)/2
     */
    @TableField(exist = false)
    @Schema(hidden = true)
    private BigDecimal middle = BigDecimal.ZERO;

    /**
     * 高低价差值
     */
    @TableField(exist = false)
    @Schema(hidden = true)
    private BigDecimal range = BigDecimal.ZERO;

    /**
     * 开盘收盘价差值
     */
    @TableField(exist = false)
    @Schema(hidden = true)
    private BigDecimal entityRange = BigDecimal.ZERO;

    @TableField("rise")
    @Schema(description = "涨幅，百分比")
    private String rise;
}

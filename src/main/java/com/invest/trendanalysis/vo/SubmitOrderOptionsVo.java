package com.invest.trendanalysis.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.longport.trade.OrderSide;
import com.longport.trade.OrderType;
import com.longport.trade.OutsideRTH;
import com.longport.trade.TimeInForceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 提交订单信息
 */
@Data
@Schema(description = "提交订单信息")
public class SubmitOrderOptionsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4673239519935785164L;
    @Schema(description = "股票代码，使用 ticker.region 格式，例如：AAPL.US",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String symbol;
    @Schema(description = """
            订单类型，
            LO，限价单，4
            ELO，增强限价单，
            MO，市价单，
            AO，竞价市价单，
            ALO，竞价限价单，
            ODD，碎股单挂单，
            LIT，触价限价单，
            MIT，触价市价单，
            TSLPAMT，跟踪止损限价单 (跟踪金额)，
            TSLPPCT，跟踪止损限价单 (跟踪涨跌幅)，
            SLO，特殊限价单，不支持改单
            """,
            requiredMode = Schema.RequiredMode.REQUIRED)
    private OrderType orderType;
    @Schema(description = "买卖方向，BUY，买入，SELL，卖出",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private OrderSide side;
    @Schema(description = "下单数量",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal submittedQuantity;
    @Schema(description = """
            订单有效期类型，
            DAY，当日有效，
            GTC，撤单前有效，
            GTD，到期前有效
            """,
            requiredMode = Schema.RequiredMode.REQUIRED)
    private TimeInForceType timeInForce;
    @Schema(description = "下单价格，LO/ELO/ALO/ODD/LIT订单必填")
    private BigDecimal submittedPrice;
    @Schema(description = "触发价格，LIT/MIT订单必填")
    private BigDecimal triggerPrice;
    @Schema(description = "指定价差，例如 \"1.2\" 表示价差 1.2 USD (如果是美股)，TSLPAMT/TSLPPCT订单必填")
    private BigDecimal limitOffset;
    @Schema(description = "跟踪金额，TSLPAMT订单必填")
    private BigDecimal trailingAmount;
    @Schema(description = "跟踪涨跌幅，单位为百分比，例如 \"2.5\" 表示 \"2.5%\"，TSLPPCT订单必填")
    private BigDecimal trailingPercent;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "长期单过期时间，格式为 YYYY-MM-DD, 例如：2022-12-05，time_in_force 为 GTD 时必填")
    private LocalDate expireDate;
    @Schema(description = """
            是否允许盘前盘后，美股必填，
            RTH_ONLY - 不允许盘前盘后，
            ANY_TIME - 允许盘前盘后，
            OVERNIGHT - 夜盘
            """)
    private OutsideRTH outsideRth;
    @Schema(description = "备注 (最大 64 字符)")
    private String remark;
}
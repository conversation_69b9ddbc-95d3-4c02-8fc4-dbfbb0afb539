package com.invest.trendanalysis.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longport.quote.Security;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.Setter;

/**
 * 标的信息
 */
@Getter
@Setter
@Tag(name = "标的信息")
@TableName("stock")
public class SecurityVo extends Security {
    @Schema(description = "标的代码")
    @TableField("symbol")
    private String symbol;
    @Schema(description = "中文名称")
    @TableField("name_cn")
    private String nameCn;
    @Schema(description = "英文名称")
    @TableField("name_en")
    private String nameEn;
    @Schema(description = "繁体名称")
    @TableField("name_hk")
    private String nameHk;

    @Override
    public String toString() {
        return "Security [symbol=" + symbol + ", nameCn=" + nameCn + ", nameEn=" + nameEn + ", nameHk=" + nameHk + "]";
    }
}
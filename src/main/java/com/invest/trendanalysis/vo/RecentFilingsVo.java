package com.invest.trendanalysis.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "报告属性")
public class RecentFilingsVo {
    @Schema(description = "SEC访问号，每个提交文件的唯一标识符")
    private List<String> accessionNumber;
    @Schema(description = "文件提交给SEC的日期")
    private List<String> filingDate;
    @Schema(description = "报告所涵盖的期间结束日期")
    private List<String> reportDate;
    @Schema(description = "SEC正式接受文件的精确时间戳")
    private List<String> acceptanceDateTime;
    @Schema(description = "相关的联邦证券法规")
    private List<String> act;
    @Schema(description = "SEC标准表格类型")
    private List<String> form;
    @Schema(description = "SEC内部文件编号")
    private List<String> fileNumber;
    @Schema(description = "微缩胶片编号(历史遗留)")
    private List<String> filmNumber;
    @Schema(description = "8-K表格中的具体条目编号，\"2.02,9.01\" 表示条目2.02(财务结果)和9.01(财务报表)，仅适用于8-K表格，其他表格此字段为空")
    private List<String> items;
    @JsonProperty("core_type")
    @Schema(description = "文件的标准化类型代码，比form字段更标准化")
    private List<String> coreType;
    @Schema(description = "主要文档的文件大小，单位为字节")
    private List<String> size;
    @Schema(description = "是否包含XBRL格式的结构化数据，1，是，0，否")
    private List<String> isXBRL;
    @Schema(description = "是否使用内联XBRL格式(iXBRL)，1，是，0，否")
    private List<String> isInlineXBRL;
    @Schema(description = "主要文档的文件名")
    private List<String> primaryDocument;
    @Schema(description = "主要文档的描述")
    private List<String> primaryDocDescription;
}

package com.invest.trendanalysis.vo.mapper;

import com.invest.trendanalysis.vo.SubmitOrderOptionsVo;
import com.longport.trade.SubmitOrderOptions;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SubmitOrderOptionsMapper {
    SubmitOrderOptionsMapper INSTANCE = Mappers.getMapper(SubmitOrderOptionsMapper.class);
    @Mappings({
            @Mapping(source = "symbol", target = "symbol"),
            @Mapping(source = "orderType", target = "orderType"),
            @Mapping(source = "side", target = "side"),
            @Mapping(source = "submittedQuantity", target = "submittedQuantity"),
            @Mapping(source = "timeInForce", target = "timeInForce"),
            @Mapping(source = "submittedPrice", target = "submittedPrice"),
            @Mapping(source = "triggerPrice", target = "triggerPrice"),
            @Mapping(source = "limitOffset", target = "limitOffset"),
            @Mapping(source = "trailingAmount", target = "trailingAmount"),
            @Mapping(source = "trailingPercent", target = "trailingPercent"),
            @Mapping(source = "expireDate", target = "expireDate"),
            @Mapping(source = "outsideRth", target = "outsideRth"),
            @Mapping(source = "remark", target = "remark"),
    })
    SubmitOrderOptions submitOrderOptionsVoToSubmitOrderOptions(SubmitOrderOptionsVo submitOrderOptionsVo);
}

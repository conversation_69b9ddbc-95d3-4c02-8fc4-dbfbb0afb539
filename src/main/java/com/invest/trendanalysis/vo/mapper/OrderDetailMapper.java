package com.invest.trendanalysis.vo.mapper;

import com.invest.trendanalysis.vo.OrderDetailVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OrderDetailMapper {
    OrderDetailMapper INSTANCE = Mappers.getMapper(OrderDetailMapper.class);
    @Mapping(source = "history", target = "history")
    OrderDetailVo orderDetailToOrderDetailVo(com.longport.trade.OrderDetail orderDetail);
}
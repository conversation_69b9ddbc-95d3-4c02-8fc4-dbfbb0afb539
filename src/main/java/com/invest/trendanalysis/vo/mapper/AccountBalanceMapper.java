package com.invest.trendanalysis.vo.mapper;

import com.invest.trendanalysis.vo.AccountBalanceVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AccountBalanceMapper {
    AccountBalanceMapper INSTANCE = Mappers.getMapper(AccountBalanceMapper.class);
    @Mapping(source = "cashInfos", target = "cashInfos")
    AccountBalanceVo accountBalanceToAccountBalanceVo(com.longport.trade.AccountBalance accountBalance);
}
package com.invest.trendanalysis.vo.mapper;

import com.invest.trendanalysis.vo.EstimateMaxPurchaseQuantityOptionsVo;
import com.longport.trade.EstimateMaxPurchaseQuantityOptions;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EstimateMaxPurchaseQuantityOptionsMapper {
    EstimateMaxPurchaseQuantityOptionsMapper INSTANCE = Mappers.getMapper(EstimateMaxPurchaseQuantityOptionsMapper.class);
    @Mapping(source = "orderType", target = "orderType")
    @Mapping(source = "side", target = "side")
    EstimateMaxPurchaseQuantityOptions estimateMaxPurchaseQuantityOptionsVoToEstimateMaxPurchaseQuantityOptions(EstimateMaxPurchaseQuantityOptionsVo EstimateMaxPurchaseQuantityOptionsVo);
}

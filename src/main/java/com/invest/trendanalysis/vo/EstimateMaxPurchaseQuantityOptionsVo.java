package com.invest.trendanalysis.vo;

import com.longport.trade.OrderSide;
import com.longport.trade.OrderType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 预估最大购买数量参数
 */
@Data
@Schema(description = "预估最大购买数量参数")
public class EstimateMaxPurchaseQuantityOptionsVo {
    @Schema(description = "股票代码，使用 ticker.region 格式，例如：AAPL.US",requiredMode = Schema.RequiredMode.REQUIRED)
    private String symbol;
    @Schema(description = """
            订单类型，
            LO，限价单，4
            ELO，增强限价单，
            MO，市价单，
            AO，竞价市价单，
            ALO，竞价限价单，
            ODD，碎股单挂单，
            LIT，触价限价单，
            MIT，触价市价单，
            TSLPAMT，跟踪止损限价单 (跟踪金额)，
            TSLPPCT，跟踪止损限价单 (跟踪涨跌幅)，
            SLO，特殊限价单，不支持改单
            """,requiredMode = Schema.RequiredMode.REQUIRED)
    private OrderType orderType;
    @Schema(description = "买卖方向，BUY，买入，SELL，卖出",requiredMode = Schema.RequiredMode.REQUIRED)
    private OrderSide side;
    @Schema(description = "预估下单价格")
    private BigDecimal price;
    @Schema(description = "结算货币")
    private String currency;
    @Schema(description = "订单 ID，获取改单预估最大购买数量时必填")
    private String orderId;
    @Schema(description = "是否支持碎股")
    private boolean fractionalShares = false;
}
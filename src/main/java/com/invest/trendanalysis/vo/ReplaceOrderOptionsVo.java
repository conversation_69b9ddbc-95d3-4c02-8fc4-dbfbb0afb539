package com.invest.trendanalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 修改订单参数
 */
@Data
@Schema(description = "修改订单参数")
public class ReplaceOrderOptionsVo{
    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;
    @Schema(description = "改单数量",requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal quantity;
    @Schema(description = "改单价格")
    private BigDecimal price;
    @Schema(description = "触发价格，LO / ELO / ALO / ODD / LIT 订单必填")
    private BigDecimal triggerPrice;
    @Schema(description = "指定价差，TSLPAMT / TSLPPCT 订单必填")
    private BigDecimal limitOffset;
    @Schema(description = "跟踪金额，TSLPAMT 订单必填")
    private BigDecimal trailingAmount;
    @Schema(description = "跟踪涨跌幅，TSLPPCT 订单必填")
    private BigDecimal trailingPercent;
    @Schema(description = "备注")
    private String remark;
}
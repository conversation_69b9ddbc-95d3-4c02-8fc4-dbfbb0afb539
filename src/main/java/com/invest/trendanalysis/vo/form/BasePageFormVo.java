package com.invest.trendanalysis.vo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 分页查询基类
 */
@Getter
@Setter
@Schema(description = "分页查询基类")
public class BasePageFormVo<T> {
    
    @Schema(description = "当前页")
    private Integer current;
    
    @Schema(description = "每页条数")
    private Integer size;
    
    @Schema(description = "查询条件")
    private T searchCondition;
    
    /**
     * 获取当前页，默认第1页
     */
    public Integer getCurrentPage() {
        return current != null ? current : 1;
    }
    
    /**
     * 获取每页条数，默认10条
     */
    public Integer getPageSize() {
        return size != null ? size : 10;
    }
}

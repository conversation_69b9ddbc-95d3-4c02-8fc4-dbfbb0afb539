package com.invest.trendanalysis.vo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 查询标的蜡烛图信息
 */
@Getter
@Setter
@Schema(description = "查询标的蜡烛图信息")
public class SearchCandlesticksInfoFormVo {
    @Schema(description = "起始日期，yyyy-MM-dd")
    private String startDate;
    @Schema(description = "结束日期，yyyy-MM-dd")
    private String endDate;
    @Schema(description = "标的")
    private String symbol;
}

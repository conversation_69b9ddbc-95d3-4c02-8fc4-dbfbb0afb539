package com.invest.trendanalysis.vo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 可分页的查询表单
 */
@Getter
@Setter
@Schema(description = "可分页的查询表单")
public class PageableSearchFormVo<T> {
    
    @Schema(description = "当前页")
    private Integer current;
    
    @Schema(description = "每页条数")
    private Integer size;
    
    @Schema(description = "查询条件")
    private T condition;

}

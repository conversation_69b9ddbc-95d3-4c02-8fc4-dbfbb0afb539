package com.invest.trendanalysis.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 交易时间表
 */
@Data
@Schema(description="交易时间表")
@TableName(value = "trading_session")
public class TradingSessionVo {
    /**
     * 主键
     */
    @TableId(value = "TS_ID", type = IdType.AUTO)
    @Schema(description="主键")
    private Integer tsId;

    /**
     * 交易日期
     */
    @TableField(value = "TS_DATE")
    @Schema(description="交易日期")
    private LocalDate tsDate;

    /**
     * 0，盘中交易，1，盘前交易，2，盘后交易，3，夜盘交易
     */
    @TableField(value = "TS_SESSION_TYPE")
    @Schema(description="0，盘中交易，1，盘前交易，2，盘后交易，3，夜盘交易")
    private Integer tsSessionType;

    /**
     * 交易开始时间
     */
    @TableField(value = "TS_BEGIN_TIME")
    @Schema(description="交易开始时间")
    private LocalDateTime tsBeginTime;

    /**
     * 交易结束时间
     */
    @TableField(value = "TS_END_TIME")
    @Schema(description="交易结束时间")
    private LocalDateTime tsEndTime;

}
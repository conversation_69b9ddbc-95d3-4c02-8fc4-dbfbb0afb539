package com.invest.trendanalysis.runner;

import com.invest.trendanalysis.service.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 启动时查询账户信息判断token是否有效
 */
@Slf4j
@Order(1)
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CheckTokenRunner implements CommandLineRunner {
    private final Service service;
    @Override
    public void run(String... args) throws Exception {
            service.GetAccountBalance(null);
    }
}
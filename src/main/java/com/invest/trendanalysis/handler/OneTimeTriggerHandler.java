package com.invest.trendanalysis.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.invest.trendanalysis.vo.PushQuoteVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 单次触发价格处理器
 */

@Data
@Slf4j
public class OneTimeTriggerHandler implements SelfRemovableHandler{
    @Override
    public void handlePrice(String symbol, PushQuoteVo quoteVo) throws JsonProcessingException {
        log.info("{}:{}", symbol, new ObjectMapper().registerModule(new JavaTimeModule()).writeValueAsString(quoteVo));
    }

    @Override
    public boolean shouldRemoveAfterExecution() {
//        一次性价格处理默认执行完后移除
        return SelfRemovableHandler.super.shouldRemoveAfterExecution();
    }

    @Override
    public boolean shouldExecute(String symbol, PushQuoteVo quote) {
        return SelfRemovableHandler.super.shouldExecute(symbol, quote);
    }

    @Override
    public void onError(Throwable ex) {
        SelfRemovableHandler.super.onError(ex);
    }
}

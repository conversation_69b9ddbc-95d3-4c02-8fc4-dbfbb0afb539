package com.invest.trendanalysis.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.invest.trendanalysis.vo.PushQuoteVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 组合策略管理器
 */
@Slf4j
@Data
public class CompositePriceHandler implements PriceHandler{
    private final CopyOnWriteArrayList<PriceHandler> handlers = new CopyOnWriteArrayList<>();
    /**
     * 可重入锁
     */
    private final Lock modificationLock = new ReentrantLock();
    public void addHandler(PriceHandler handler) {
//        添加策略的时候加锁，加完后解锁
        modificationLock.lock();
        try {
            handlers.add(handler);
            log.info("已经添加策略");
        } finally {
            modificationLock.unlock();
        }
    }

    public boolean removeHandler(PriceHandler handler) {
        modificationLock.lock();
        try {
            boolean remove = handlers.remove(handler);
            if (remove){
                log.info("已经移除策略");
            }
            return remove;
        } finally {
            modificationLock.unlock();
        }
    }
    @Override
    public void handlePrice(String symbol, PushQuoteVo quoteVo) throws JsonProcessingException {
        List<PriceHandler> toRemove = null;
        // 直接使用 CopyOnWriteArrayList，无需创建新的 ArrayList
        for (PriceHandler handler : handlers) {
//                如果是一次性移除的价格处理器
            if (handler instanceof SelfRemovableHandler removable) {
//                    前置逻辑执行完后
                if (removable.shouldExecute(symbol, quoteVo)) {
//                        执行业务逻辑
                    removable.handlePrice(symbol, quoteVo);
                    if (removable.shouldRemoveAfterExecution()) {
                        // 延迟初始化 toRemove 列表
                        if (toRemove == null) {
                            toRemove = new ArrayList<>();
                        }
                        toRemove.add(handler);
                    }
                }
            } else {
//                正常价格处理器
                handler.handlePrice(symbol, quoteVo);
            }
        }

        // 只在有需要移除的处理器时才加锁和执行移除操作
        if (toRemove != null && !toRemove.isEmpty()) {
            modificationLock.lock();
            try {
                handlers.removeAll(toRemove);
            } finally {
                modificationLock.unlock();
            }
        }
    }
}

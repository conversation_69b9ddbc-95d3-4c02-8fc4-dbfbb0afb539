package com.invest.trendanalysis.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(value = {Exception.class})
    @ResponseBody
    public ResponseEntity<String> exceptionHandler(Exception e){
        log.error("error:{}", e.getLocalizedMessage());
        return ResponseEntity.status(500).body(e.getMessage());
    }
}
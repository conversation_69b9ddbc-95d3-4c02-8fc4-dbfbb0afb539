package com.invest.trendanalysis.handler;

import com.invest.trendanalysis.vo.PushQuoteVo;

/**
 * 增强型价格处理接口
 */
public interface EnhancedPriceHandler extends PriceHandler{
    // 标记需要自动移除的处理器
    default boolean shouldRemoveAfterExecution() {
        return false;
    }
    // 条件判断前置钩子
    default boolean shouldExecute(String symbol, PushQuoteVo quote) {
        return true;
    }
    // 错误处理钩子
    default void onError(Throwable ex) {
        ex.printStackTrace();
    }
}

package com.invest.trendanalysis.handler.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.invest.trendanalysis.handler.PriceHandler;
import com.invest.trendanalysis.vo.PushQuoteVo;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EqualsAndHashCode
public class CustomPriceHandlerImpl implements PriceHandler {
    @Override
    public void handlePrice(String symbol,PushQuoteVo quoteVo) throws JsonProcessingException {
        log.info("{}:{}", symbol, new ObjectMapper().registerModule(new JavaTimeModule()).writeValueAsString(quoteVo));
    }
}

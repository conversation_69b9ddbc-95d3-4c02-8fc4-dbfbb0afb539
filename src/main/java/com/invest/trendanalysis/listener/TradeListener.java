package com.invest.trendanalysis.listener;


import com.invest.trendanalysis.service.Service;
import com.invest.trendanalysis.vo.DelaySubmitOrderOptionsVo;
import com.invest.trendanalysis.vo.SubmitOrderOptionsVo;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * 处理交易消息的监听器
 */
@Slf4j
@RabbitListener(queues = "TradeQueue")
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TradeListener {
    private final Service service;

    /**
     * 消息队列接收到提交订单消息
     * @param message message
     * @param delaySubmitOrderOptionsVo delaySubmitOrderOptionsVo
     * @param channel channel
     * @throws Exception Exception
     */
    @RabbitHandler
    public void submitOrder(Message message, DelaySubmitOrderOptionsVo delaySubmitOrderOptionsVo, Channel channel) throws Exception {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        String messageId = message.getMessageProperties().getMessageId();
        MessageProperties props = message.getMessageProperties();
        String exchange = props.getReceivedExchange();
        log.info("交换机：{}，消息ID：{}",exchange,messageId);
        SubmitOrderOptionsVo submitOrderOptionsVo = new SubmitOrderOptionsVo();
        BeanUtils.copyProperties(delaySubmitOrderOptionsVo,submitOrderOptionsVo);
        try {
            var response = service.SubmitOrder(submitOrderOptionsVo);
            if (response.getStatusCode().equals(HttpStatus.OK)){
                channel.basicAck(deliveryTag,false);
            }else {
                channel.basicReject(deliveryTag,false);
                log.error("提交订单消息异常，消息ID：{}",messageId);
            }
        } catch (Exception e) {
            channel.basicReject(deliveryTag,false);
            log.error("提交订单消息异常，消息ID：{}",messageId);
        }
    }
}
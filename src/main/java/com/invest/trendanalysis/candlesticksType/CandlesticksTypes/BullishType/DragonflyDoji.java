package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.math.RoundingMode;
import java.util.List;

/**
 * 蜻蜓十字星-看涨
 */
public class DragonflyDoji implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo candlestickVo = candlesticks.get(0);
            return candlestickVo.getIsDoji().equals(1)
                    && candlestickVo.getHigh().compareTo(candlestickVo.getOpen()) >= 0
                    && candlestickVo.getHigh().compareTo(candlestickVo.getClose()) >= 0
                    && candlestickVo.getHigh().subtract(candlestickVo.getOpen()).divide(candlestickVo.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.dojiPercentage) <= 0
                    && candlestickVo.getHigh().subtract(candlestickVo.getClose()).divide(candlestickVo.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.dojiPercentage) <= 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "蜻蜓十字星-看涨";
    }
}

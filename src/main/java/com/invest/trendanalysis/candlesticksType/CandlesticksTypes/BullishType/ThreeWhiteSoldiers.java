package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 三白兵-看涨
 */
public class ThreeWhiteSoldiers implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsRise().equals(1)
                    && firstCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsRise().equals(1)
                    && secondCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(secondCandlestick)
                    && thirdCandlestick.getIsRise().equals(1)
                    && thirdCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(thirdCandlestick)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getOpen()) >= 0
                    && secondCandlestick.getOpen().compareTo(thirdCandlestick.getOpen()) <= 0
                    && secondCandlestick.getOpen().compareTo(thirdCandlestick.getClose()) >= 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getClose()) >= 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getOpen()) <= 0
                    && secondCandlestick.getClose().compareTo(thirdCandlestick.getClose()) <= 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "三白兵-看涨";
    }
}

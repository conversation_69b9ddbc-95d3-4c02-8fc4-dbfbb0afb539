package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 下肩带缺口-看跌
 */
public class DownsideTasukiGap implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsRise().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsRise().equals(0)
                    && secondCandlestick.getIsDoji().equals(0)
                    && secondCandlestick.getEntityRange().compareTo(firstCandlestick.getEntityRange()) < 0
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0
                    && thirdCandlestick.getIsRise().equals(1)
                    && thirdCandlestick.getIsDoji().equals(0)
                    && thirdCandlestick.getClose().compareTo(firstCandlestick.getClose()) < 0
                    && thirdCandlestick.getClose().compareTo(secondCandlestick.getOpen()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "下肩带缺口-看跌";
    }
}

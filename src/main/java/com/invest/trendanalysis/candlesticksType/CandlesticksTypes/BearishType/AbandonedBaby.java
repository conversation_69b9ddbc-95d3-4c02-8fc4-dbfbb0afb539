package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 弃婴-看跌
 */
public class AbandonedBaby implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsRise().equals(1)
                    && firstCandlestick.getIsDoji().equals(0)
                    && secondCandlestick.getIsDoji().equals(1)
                    && secondCandlestick.getLow().compareTo(firstCandlestick.getClose()) > 0
                    && thirdCandlestick.getIsRise().equals(0)
                    && thirdCandlestick.getIsDoji().equals(0)
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getClose()) < 0
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getOpen()) < 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "弃婴-看跌";
    }
}

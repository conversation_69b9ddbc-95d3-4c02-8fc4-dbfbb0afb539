package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 长下影线-看涨
 */
public class LongLowerShadow implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo candlestick = candlesticks.get(0);
            return (candlestick.getHigh().subtract(candlestick.getOpen().max(candlestick.getClose()))
                    .compareTo(candlestick.getOpen().min(candlestick.getClose()).subtract(candlestick.getLow())) < 0)
                    && (candlestick.getEntityRange().compareTo(candlestick.getOpen().min(candlestick.getClose()).subtract(candlestick.getLow())) < 0);
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "长下影线-看涨";
    }
}

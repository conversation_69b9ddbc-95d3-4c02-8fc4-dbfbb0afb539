package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.math.RoundingMode;
import java.util.List;

/**
 * 墓碑十字星-看跌
 */
public class GravestoneDoji implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        }else {
            CandlestickVo candlestickVo = candlesticks.get(0);
            return candlestickVo.getIsDoji().equals(1)
                    && candlestickVo.getLow().compareTo(candlestickVo.getOpen()) <= 0
                    && candlestickVo.getLow().compareTo(candlestickVo.getClose()) <= 0
                    && candlestickVo.getOpen().subtract(candlestickVo.getLow()).divide(candlestickVo.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.dojiPercentage) <= 0
                    && candlestickVo.getClose().subtract(candlestickVo.getLow()).divide(candlestickVo.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.dojiPercentage) <= 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "墓碑十字星-看跌";
    }
}

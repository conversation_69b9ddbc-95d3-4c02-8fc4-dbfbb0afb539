package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.math.RoundingMode;
import java.util.List;

/**
 * 反冲-看涨
 */
public class Kicker implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getIsRise().equals(0)
                    && firstCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && firstCandlestick.getHigh().subtract(firstCandlestick.getOpen()).divide(firstCandlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage) <= 0
                    && firstCandlestick.getClose().subtract(firstCandlestick.getLow()).divide(firstCandlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage) <= 0
                    && secondCandlestick.getIsRise().equals(1)
                    && secondCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(secondCandlestick)
                    && secondCandlestick.getHigh().subtract(secondCandlestick.getClose()).divide(secondCandlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage) <= 0
                    && secondCandlestick.getOpen().subtract(secondCandlestick.getLow()).divide(secondCandlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage) <= 0
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getOpen()) > 0;

        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "反冲-看涨";
    }
}

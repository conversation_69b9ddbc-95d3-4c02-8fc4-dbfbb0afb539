package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 上升三法-看涨
 */
public class RisingThreeMethods implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 5) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            CandlestickVo fourthCandlestick = candlesticks.get(3);
            CandlestickVo fifthCandlestick = candlesticks.get(4);
            return firstCandlestick.getIsRise().equals(1)
                    && firstCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsRise().equals(1)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getOpen()) > 0
                    && thirdCandlestick.getIsRise().equals(1)
                    && thirdCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0
                    && thirdCandlestick.getClose().compareTo(firstCandlestick.getOpen()) > 0
                    && fourthCandlestick.getIsRise().equals(1)
                    && fourthCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0
                    && fourthCandlestick.getClose().compareTo(firstCandlestick.getOpen()) > 0
                    && fifthCandlestick.getIsRise().equals(1)
                    && fifthCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(fifthCandlestick)
                    && fifthCandlestick.getClose().compareTo(firstCandlestick.getClose()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "上升三法-看涨";
    }
}

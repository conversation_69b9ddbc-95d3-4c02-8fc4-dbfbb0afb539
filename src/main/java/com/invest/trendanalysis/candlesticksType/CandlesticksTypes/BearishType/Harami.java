package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 孕线-看跌
 */
public class Hara<PERSON> implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getIsRise().equals(1) &&
                    firstCandlestick.getIsDoji().equals(0) &&
                    secondCandlestick.getIsRise().equals(0) &&
                    secondCandlestick.getIsDoji().equals(0) &&
                    secondCandlestick.getClose().compareTo(firstCandlestick.getOpen()) > 0 &&
                    secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "孕线-看跌";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 上吊线-看跌
 */
public class HangingMan implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            return firstCandlestick.getIsRise().equals(0)
                    && firstCandlestick.getIsDoji().equals(0)
                    && !CandlestickUtil.longEntity(firstCandlestick)
                    && firstCandlestick.getClose().subtract(firstCandlestick.getLow()).compareTo(firstCandlestick.getHigh().subtract(firstCandlestick.getOpen())) > 0
                    && firstCandlestick.getClose().subtract(firstCandlestick.getLow()).compareTo(firstCandlestick.getEntityRange()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "上吊线-看跌";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 晨星十字星-看涨
 */
public class MorningDojiStar implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        }else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsRise().equals(0)
                    && secondCandlestick.getIsDoji().equals(1)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0
                    && thirdCandlestick.getIsRise().equals(1)
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getClose()) > 0
                    && thirdCandlestick.getClose().compareTo(firstCandlestick.getMiddle()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "晨星十字星-看涨";
    }
}

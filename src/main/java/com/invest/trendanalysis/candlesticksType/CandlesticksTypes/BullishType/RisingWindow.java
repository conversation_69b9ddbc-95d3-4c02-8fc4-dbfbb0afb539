package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 上升窗口-看涨
 */
public class RisingWindow implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        }else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getHigh().compareTo(secondCandlestick.getLow()) < 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "上升窗口-看涨";
    }
}

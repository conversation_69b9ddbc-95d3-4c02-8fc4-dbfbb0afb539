package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 弃婴-看涨
 */
public class AbandonedBaby implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        }else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsRise().equals(0)
                    && firstCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsDoji().equals(1)
                    && secondCandlestick.getHigh().compareTo(firstCandlestick.getClose()) < 0
                    && thirdCandlestick.getIsRise().equals(1)
                    && thirdCandlestick.getIsDoji().equals(0)
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getClose()) > 0
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getOpen()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "弃婴-看涨";
    }
}

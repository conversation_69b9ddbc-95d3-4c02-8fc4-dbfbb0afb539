package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 三乌鸦-看跌
 */
public class ThreeBlackCrows implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsRise().equals(0)
                    && firstCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsRise().equals(0)
                    && secondCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(secondCandlestick)
                    && thirdCandlestick.getIsRise().equals(0)
                    && thirdCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(thirdCandlestick)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getOpen()) <= 0
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) >=0
                    && secondCandlestick.getOpen().compareTo(thirdCandlestick.getOpen()) >= 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getClose()) <=0
                    && secondCandlestick.getClose().compareTo(thirdCandlestick.getClose()) >=0
                    && secondCandlestick.getClose().compareTo(thirdCandlestick.getOpen()) <=0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "三乌鸦-看跌";
    }
}

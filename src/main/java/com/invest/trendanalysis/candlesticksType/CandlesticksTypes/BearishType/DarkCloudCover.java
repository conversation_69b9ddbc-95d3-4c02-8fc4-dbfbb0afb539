package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 乌云盖顶-看跌
 */
public class DarkCloudCover implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getIsRise().equals(1)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsRise().equals(0)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getHigh()) > 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getMiddle()) < 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "乌云盖顶-看跌";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 锤形线-看涨
 */
public class Hammer implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        }else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            return firstCandlestick.getIsRise().equals(1)
                    && firstCandlestick.getIsDoji().equals(0)
                    && !CandlestickUtil.longEntity(firstCandlestick)
                    && firstCandlestick.getOpen().subtract(firstCandlestick.getLow()).compareTo(firstCandlestick.getHigh().subtract(firstCandlestick.getClose()))>0
                    && firstCandlestick.getOpen().subtract(firstCandlestick.getLow()).compareTo(firstCandlestick.getEntityRange())>0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "锤形线-看涨";
    }
}

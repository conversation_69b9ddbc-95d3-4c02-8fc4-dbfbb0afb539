package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 下降窗口-看跌
 */
public class FallingWindow implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getLow().compareTo(secondCandlestick.getHigh()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "下降窗口-看跌 ";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.FlatType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 白色纺锤线-看平
 */
public class SpinningTopWhite implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo candlestick = candlesticks.get(0);
            return candlestick.getIsRise().equals(1)
                    && candlestick.getIsDoji().equals(0)
                    && !CandlestickUtil.longEntity(candlestick)
                    && candlestick.getHigh().subtract(candlestick.getClose()).compareTo(candlestick.getEntityRange()) > 0
                    && candlestick.getOpen().subtract(candlestick.getLow()).compareTo(candlestick.getEntityRange()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "白色纺锤线-看平";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 长上影线-看跌
 */
public class LongUpperShadow implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo candlestick = candlesticks.get(0);
            return (candlestick.getHigh().subtract(candlestick.getOpen().max(candlestick.getClose()))
                    .compareTo(candlestick.getOpen().min(candlestick.getClose()).subtract(candlestick.getLow())) > 0)
                    && (candlestick.getEntityRange().compareTo(candlestick.getHigh().subtract(candlestick.getOpen().max(candlestick.getClose()))) < 0);
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "长上影线-看跌";
    }
}

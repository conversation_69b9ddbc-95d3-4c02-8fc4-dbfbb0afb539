package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 穿刺-看涨
 */
public class Piercing implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getIsRise().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsRise().equals(1)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getLow()) > 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getMiddle()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "穿刺-看涨";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 十字星-看涨
 */
public class DojiStar implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size()!=(2)) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            if (firstCandlestick.getIsRise().equals(0)
            ){
                return secondCandlestick.getIsDoji().equals(1) &&
                        secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0;
            }else {
                return false;
            }
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "十字星-看涨";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 暮星十字星-看跌
 */
public class EveningDojiStar implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        }else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsRise().equals(1)
                    && secondCandlestick.getIsDoji().equals(1)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) > 0
                    && thirdCandlestick.getIsRise().equals(0)
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getClose()) < 0
                    && thirdCandlestick.getClose().compareTo(firstCandlestick.getMiddle()) < 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "暮星十字星-看跌";
    }
}

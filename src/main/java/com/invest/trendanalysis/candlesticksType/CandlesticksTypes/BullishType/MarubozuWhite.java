package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.math.RoundingMode;
import java.util.List;

/**
 * 白色光头光脚-看涨
 */
public class MarubozuWhite implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo candlestick = candlesticks.get(0);
            return candlestick.getIsRise().equals(1)
                    && candlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(candlestick)
                    && candlestick.getHigh().subtract(candlestick.getClose()).divide(candlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage) <= 0
                    && candlestick.getOpen().subtract(candlestick.getLow()).divide(candlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage) <= 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "白色光头光脚-看涨";
    }
}

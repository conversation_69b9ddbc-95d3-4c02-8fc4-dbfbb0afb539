package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.math.RoundingMode;
import java.util.List;

/**
 * 镊顶-看跌
 */
public class TweezerTop implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getIsRise().equals(1)
                    && firstCandlestick.getIsDoji().equals(0)
                    && CandlestickUtil.longEntity(firstCandlestick)
                    && secondCandlestick.getIsRise().equals(0)
                    && secondCandlestick.getIsDoji().equals(0)
                    && firstCandlestick.getHigh().subtract(secondCandlestick.getHigh()).abs().divide(firstCandlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage) <= 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "镊顶-看跌 ";
    }
}

package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 倒锤型-看涨
 */
public class InvertedHammer implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo candlestick = candlesticks.get(0);
            return candlestick.getIsRise().equals(1)
                    && candlestick.getIsDoji().equals(0)
                    && candlestick.getHigh().subtract(candlestick.getClose()).compareTo(candlestick.getOpen().subtract(candlestick.getLow())) > 0
                    && candlestick.getHigh().subtract(candlestick.getClose()).compareTo(candlestick.getEntityRange())>0
                    && !CandlestickUtil.longEntity(candlestick);
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "倒锤型-看涨";
    }
}

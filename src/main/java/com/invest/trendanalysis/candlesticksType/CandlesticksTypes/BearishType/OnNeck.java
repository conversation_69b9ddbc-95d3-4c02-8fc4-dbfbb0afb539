package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.math.RoundingMode;
import java.util.List;

/**
 * 颈上线-看跌
 */
public class OnNeck implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 2) {
            return false;
        }else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            return firstCandlestick.getIsRise().equals(0)
                    && firstCandlestick.getIsDoji().equals(0)
                    && secondCandlestick.getRange().compareTo(firstCandlestick.getRange())<0
                    && secondCandlestick.getIsRise().equals(1)
                    && !CandlestickUtil.longEntity(secondCandlestick)
                    && firstCandlestick.getLow().subtract(secondCandlestick.getClose()).abs().divide(firstCandlestick.getRange(), RoundingMode.HALF_UP).compareTo(CandlestickUtil.shadowPercentage)<=0;

        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "颈上线-看跌";
    }
}

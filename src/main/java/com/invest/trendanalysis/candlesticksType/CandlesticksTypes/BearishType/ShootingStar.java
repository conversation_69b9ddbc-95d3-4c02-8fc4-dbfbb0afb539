package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BearishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.util.CandlestickUtil;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 射击之星-看跌
 */
public class ShootingStar implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 1) {
            return false;
        } else {
            CandlestickVo candlestick = candlesticks.get(0);
            return candlestick.getIsRise().equals(0)
                    && candlestick.getIsDoji().equals(0)
                    && candlestick.getHigh().subtract(candlestick.getOpen()).compareTo(candlestick.getClose().subtract(candlestick.getLow())) > 0
                    && candlestick.getHigh().subtract(candlestick.getOpen()).compareTo(candlestick.getEntityRange()) > 0
                    && !CandlestickUtil.longEntity(candlestick);
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "射击之星-看跌";
    }
}

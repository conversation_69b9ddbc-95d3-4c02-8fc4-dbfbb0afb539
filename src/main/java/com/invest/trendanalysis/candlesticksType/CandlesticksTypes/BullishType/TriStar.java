package com.invest.trendanalysis.candlesticksType.CandlesticksTypes.BullishType;

import com.invest.trendanalysis.candlesticksType.CandlesticksTypeInterface;
import com.invest.trendanalysis.vo.CandlestickVo;

import java.util.List;

/**
 * 三星-看涨
 */
public class TriStar implements CandlesticksTypeInterface {
    @Override
    public boolean analyze(List<CandlestickVo> candlesticks) {
        if (candlesticks.size() != 3) {
            return false;
        } else {
            CandlestickVo firstCandlestick = candlesticks.get(0);
            CandlestickVo secondCandlestick = candlesticks.get(1);
            CandlestickVo thirdCandlestick = candlesticks.get(2);
            return firstCandlestick.getIsDoji().equals(0)
                    && secondCandlestick.getIsDoji().equals(0)
                    && thirdCandlestick.getIsDoji().equals(0)
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getOpen()) < 0
                    && secondCandlestick.getOpen().compareTo(firstCandlestick.getClose()) < 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getOpen()) < 0
                    && secondCandlestick.getClose().compareTo(firstCandlestick.getClose()) < 0
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getOpen()) > 0
                    && thirdCandlestick.getOpen().compareTo(secondCandlestick.getClose()) > 0
                    && thirdCandlestick.getClose().compareTo(secondCandlestick.getOpen()) > 0
                    && thirdCandlestick.getClose().compareTo(secondCandlestick.getClose()) > 0;
        }
    }

    @Override
    public String getCandlesticksTypeName() {
        return "三星-看涨";
    }
}

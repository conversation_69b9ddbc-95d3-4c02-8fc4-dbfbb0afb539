CREATE TABLE `trading_session` (
    `TS_ID` int(11) NOT NULL DEFAULT '0' COMMENT '主键',
    `TS_DATE` date DEFAULT NULL COMMENT '交易日期',
    `TS_SESSION_TYPE` int(11) DEFAULT NULL COMMENT '0，盘中交易，1，盘前交易，2，盘后交易，3，夜盘交易',
    `TS_BEGIN_TIME` datetime DEFAULT NULL COMMENT '交易开始时间',
    `TS_END_TIME` datetime DEFAULT NULL COMMENT '交易结束时间',
     PRIMARY KEY (`TS_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='交易时间表'
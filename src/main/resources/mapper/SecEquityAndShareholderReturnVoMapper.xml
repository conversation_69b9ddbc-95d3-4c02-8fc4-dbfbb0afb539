<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.SecEquityAndShareholderReturnVoMapper">
  <resultMap id="BaseResultMap" type="com.invest.trendanalysis.vo.sec.SecEquityAndShareholderReturnVo">
    <!--@mbg.generated-->
    <!--@Table trend.sec_equity_and_shareholder_return-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="report_id" jdbcType="INTEGER" property="reportId" />
    <result column="common_stock_shares_outstanding" jdbcType="BIGINT" property="commonStockSharesOutstanding" />
    <result column="weighted_average_shares_basic" jdbcType="BIGINT" property="weightedAverageSharesBasic" />
    <result column="weighted_average_shares_diluted" jdbcType="BIGINT" property="weightedAverageSharesDiluted" />
    <result column="repurchase_authorized_amount" jdbcType="DECIMAL" property="repurchaseAuthorizedAmount" />
    <result column="repurchase_remaining_amount" jdbcType="DECIMAL" property="repurchaseRemainingAmount" />
    <result column="common_stock_shares_authorized" jdbcType="BIGINT" property="commonStockSharesAuthorized" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_id, common_stock_shares_outstanding, weighted_average_shares_basic, weighted_average_shares_diluted, 
    repurchase_authorized_amount, repurchase_remaining_amount, common_stock_shares_authorized
  </sql>
</mapper>
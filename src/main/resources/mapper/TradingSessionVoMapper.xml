<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.TradingSessionVoMapper">
  <resultMap id="BaseResultMap" type="com.invest.trendanalysis.vo.TradingSessionVo">
    <!--@mbg.generated-->
    <!--@Table trading_session-->
    <id column="TS_ID" jdbcType="INTEGER" property="tsId" />
    <result column="TS_DATE" jdbcType="DATE" property="tsDate" />
    <result column="TS_SESSION_TYPE" jdbcType="INTEGER" property="tsSessionType" />
    <result column="TS_BEGIN_TIME" jdbcType="TIMESTAMP" property="tsBeginTime" />
    <result column="TS_END_TIME" jdbcType="TIMESTAMP" property="tsEndTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TS_ID, TS_DATE, TS_SESSION_TYPE, TS_BEGIN_TIME, TS_END_TIME
  </sql>
</mapper>
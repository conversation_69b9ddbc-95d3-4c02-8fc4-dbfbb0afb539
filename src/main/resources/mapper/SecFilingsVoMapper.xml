<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.SecFilingsVoMapper">
  <resultMap id="BaseResultMap" type="com.invest.trendanalysis.vo.sec.SecFilingsVo">
    <!--@mbg.generated-->
    <!--@Table trend.sec_filings-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cik" jdbcType="VARCHAR" property="cik" />
    <result column="accessionNumber" jdbcType="VARCHAR" property="accessionnumber" />
    <result column="filingDate" jdbcType="VARCHAR" property="filingdate" />
    <result column="reportDate" jdbcType="VARCHAR" property="reportdate" />
    <result column="form" jdbcType="VARCHAR" property="form" />
    <result column="primaryDocument" jdbcType="VARCHAR" property="primarydocument" />
    <result column="primaryDocDescription" jdbcType="VARCHAR" property="primarydocdescription" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cik, accessionNumber, filingDate, reportDate, form, primaryDocument, primaryDocDescription
  </sql>
</mapper>